# =============================================================================
# CHATBOT SUPPORT APPLICATION ENVIRONMENT CONFIGURATION (EXAMPLE)
# =============================================================================
# Copy this file to .env and update the values according to your environment
# Make sure to keep .env file secure and never commit it to version control

# =============================================================================
# APPLICATION ENVIRONMENT
# =============================================================================
ENV=local
# Options: local, development, staging, production

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_HOST=db
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_secure_password_here
DB_NAME=ragchatbot
DATABASE_LOGGING=true

# Database Secret ARN (for AWS RDS)
DB_SECRET_ARN=

# =============================================================================
# AWS S3 CONFIGURATION
# =============================================================================
# AWS Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# S3 Bucket Configuration
S3_BUCKET_NAME=your_s3_bucket_name
S3_PREFIX=chatbot_support

# =============================================================================
# VECTOR STORE CONFIGURATION
# =============================================================================
VECTOR_STORE_TYPE=chroma
VECTOR_STORE_DIR=/app/central_vector_store
USER_DATA_DIR=/app/user_data

# =============================================================================
# JWT AUTHENTICATION CONFIGURATION
# =============================================================================
# IMPORTANT: Change this secret key in production!
JWT_SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# GOOGLE AI CONFIGURATION
# =============================================================================
# Google Service Account File Path
GOOGLE_SERVICE_ACCOUNT_FILE=/app/google_credentials.json

# Google Cloud Project ID
GOOGLE_PROJECT_ID=your_google_project_id

# Alternative: Google API Key (if not using service account)
# GOOGLE_API_KEY=your_google_api_key

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins
# Use * for all origins (not recommended for production)
CORS_ORIGINS=*

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
# Backend API URL for frontend
REACT_APP_API_URL=http://localhost:8000

# Frontend Environment
REACT_APP_ENV=production

# Application Version
REACT_APP_VERSION=1.0.0

# Backend API URL (internal Docker network)
BACKEND_API_URL=http://backend:8000

# =============================================================================
# MYSQL DATABASE CONFIGURATION
# =============================================================================
# MySQL Root User Configuration
MYSQL_ROOT_USER=root
MYSQL_ROOT_PASSWORD=your_secure_password_here
MYSQL_DATABASE=ragchatbot

# =============================================================================
# LOCALSTACK CONFIGURATION (for local AWS S3 simulation)
# =============================================================================
# LocalStack AWS Configuration
LOCALSTACK_AWS_DEFAULT_REGION=us-east-1
LOCALSTACK_AWS_ACCESS_KEY_ID=test
LOCALSTACK_AWS_SECRET_ACCESS_KEY=test

# =============================================================================
# OPTIONAL: OPENAI CONFIGURATION
# =============================================================================
# OpenAI API Key (if using OpenAI models)
# OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# OPTIONAL: ADDITIONAL AI MODEL CONFIGURATION
# =============================================================================
# Embedding Model Configuration
# EMBEDDING_MODEL=all-MiniLM-L6-v2

# LLM Model Configuration
# LLM_MODEL_NAME=gemini-pro

# Vector Store Configuration
# USE_COMPRESSION=false
# TOP_K=4
# MAX_MEMORY_MESSAGES=10

# =============================================================================
# DEVELOPMENT/DEBUGGING CONFIGURATION
# =============================================================================
# Python Configuration
PYTHONUNBUFFERED=1

# Logging Configuration
# LOG_LEVEL=INFO

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit this .env file to version control
# 2. Use strong, unique passwords for all services
# 3. Change JWT_SECRET_KEY to a long, random string in production
# 4. Use proper AWS IAM roles and policies
# 5. Restrict CORS_ORIGINS to specific domains in production
# 6. Use environment-specific configurations for different deployments

# =============================================================================
# QUICK SETUP CHECKLIST
# =============================================================================
# [ ] Update DB_PASSWORD with a strong password
# [ ] Set your AWS credentials (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
# [ ] Set your S3 bucket name (S3_BUCKET_NAME)
# [ ] Set your Google Project ID (GOOGLE_PROJECT_ID)
# [ ] Generate a strong JWT_SECRET_KEY
# [ ] Update CORS_ORIGINS for production
# [ ] Place your google_credentials.json file in the project root
# [ ] Review and update any optional configurations as needed
