# AWS Deployment Guide

This guide provides instructions for deploying the RAG Chatbot application on AWS.

## Prerequisites

1. An AWS account with appropriate permissions
2. AWS CLI installed and configured
3. <PERSON><PERSON> and Docker Compose installed
4. Git installed

## Deployment Steps

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd <repository-directory>
```

### 2. Configure Environment Variables

Create a `.env` file based on the provided `.env.example`:

```bash
cp .env.example .env
```

Edit the `.env` file and update the following variables:

- `AWS_ACCESS_KEY_ID`: Your AWS access key
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
- `S3_BUCKET_NAME`: Your S3 bucket name
- `JWT_SECRET_KEY`: A secure random string for JWT token signing
- `GOOGLE_PROJECT_ID`: Your Google Cloud project ID
- `REACT_APP_API_URL`: The public URL where your API will be accessible

### 3. Update API URL for Frontend

Use the provided script to update the API URL for the frontend:

```bash
chmod +x update-api-url.sh
./update-api-url.sh https://your-api-domain.com
```

### 4. Build and Start the Application

```bash
docker-compose build
docker-compose up -d
```

### 5. Initialize the Database

The database will be automatically initialized with the required tables and a default admin user.

Default admin credentials:
- Username: `admin`
- Password: `admin`

**Important**: Change the admin password immediately after the first login.

### 6. Configure AWS Services

#### EC2 Instance

1. Launch an EC2 instance with at least 2 vCPUs and 4GB RAM
2. Configure security groups to allow traffic on ports 80 (HTTP) and 8000 (API)
3. Install Docker and Docker Compose on the instance

#### S3 Bucket

1. Create an S3 bucket for document storage
2. Update the `S3_BUCKET_NAME` in your `.env` file
3. Configure CORS for the S3 bucket if needed

#### RDS (Optional)

If you prefer to use Amazon RDS instead of the containerized MySQL:

1. Create an RDS MySQL instance
2. Update the database connection parameters in your `.env` file:
   - `DB_HOST`: Your RDS endpoint
   - `DB_PORT`: Your RDS port (usually 3306)
   - `DB_USER`: Your RDS username
   - `DB_PASSWORD`: Your RDS password
   - `DB_NAME`: Your RDS database name

### 7. Set Up Domain and SSL (Optional)

1. Register a domain or use an existing one
2. Configure Route 53 to point to your EC2 instance
3. Set up an SSL certificate using AWS Certificate Manager
4. Configure a load balancer to handle HTTPS traffic

### 8. Monitoring and Logging

1. Set up CloudWatch for monitoring
2. Configure CloudWatch Logs for log aggregation
3. Set up alarms for critical metrics

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check that the database credentials are correct
   - Verify that the security group allows traffic on the database port

2. **S3 Access Issues**:
   - Verify that the AWS credentials have the necessary permissions
   - Check that the S3 bucket exists and is accessible

3. **API Not Accessible**:
   - Check that the security group allows traffic on port 8000
   - Verify that the API service is running with `docker-compose ps`

### Logs

To view logs for troubleshooting:

```bash
# View API logs
docker-compose logs api

# View frontend logs
docker-compose logs frontend

# View database logs
docker-compose logs db
```

## Backup and Restore

### Database Backup

```bash
docker-compose exec db mysqldump -u raguser -pragpassword ragchatbot > backup.sql
```

### Database Restore

```bash
cat backup.sql | docker-compose exec -T db mysql -u raguser -pragpassword ragchatbot
```

### Vector Store Backup

The vector store data is stored in a Docker volume. To back it up:

```bash
docker run --rm -v ragchatbot_vector-store-volume:/data -v $(pwd):/backup alpine tar -czf /backup/vector-store-backup.tar.gz /data
```

### Vector Store Restore

```bash
docker run --rm -v ragchatbot_vector-store-volume:/data -v $(pwd):/backup alpine sh -c "rm -rf /data/* && tar -xzf /backup/vector-store-backup.tar.gz -C /"
```

## Scaling

For higher load scenarios, consider:

1. Using Amazon RDS for the database
2. Setting up an Auto Scaling Group for the API service
3. Using Amazon ElastiCache for caching
4. Implementing a load balancer for the API service
