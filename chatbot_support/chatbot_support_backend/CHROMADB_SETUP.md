# ChromaDB External Setup Guide

This guide explains how to set up and use external ChromaDB with your RAG chatbot system.

## Current Configuration

Your system is now configured to use **external ChromaDB** for vector storage. Here's where your vector data is stored:

### External ChromaDB Server
- **Service**: Dedicated ChromaDB container
- **Host**: `chromadb` (Docker service name)
- **Port**: `8001` (external) → `8000` (internal)
- **Data Storage**: Docker volume `chromadb-data`
- **Collection**: `chatbot_collection`

## Configuration Files

### 1. Environment Variables (`.env`)
```bash
# Vector store configuration
VECTOR_STORE_TYPE=chroma
USE_EXTERNAL_CHROMA=true
CHROMA_HOST=chromadb
CHROMA_PORT=8000
```

### 2. Docker Compose Services
Your `docker-compose.yml` includes:
- **ChromaDB Service**: Dedicated vector database server
- **API Service**: Connects to ChromaDB for vector operations
- **Persistent Storage**: `chromadb-data` volume

## How It Works

### Vector Data Flow
1. **PDF Upload** → Document processing → Text chunking
2. **Embedding Generation** → HuggingFace embeddings (`all-MiniLM-L6-v2`)
3. **Vector Storage** → External ChromaDB server
4. **Similarity Search** → ChromaDB retrieval → LLM response

### Code Implementation
Your vector store automatically detects external ChromaDB:

```python
# Automatic detection in ragchatbot/embedding/vector_store.py
if USE_EXTERNAL_CHROMA:
    client = chromadb.HttpClient(host=CHROMA_HOST, port=CHROMA_PORT)
    vector_store = Chroma(
        embedding_function=embeddings,
        client=client,
        collection_name="chatbot_collection"
    )
else:
    # Falls back to local file-based ChromaDB
    vector_store = Chroma(
        embedding_function=embeddings,
        persist_directory="/app/central_vector_store"
    )
```

## Deployment Instructions

### 1. Start the System
```bash
# Copy environment configuration
cp .env.example .env

# Edit .env to ensure external ChromaDB is enabled
# USE_EXTERNAL_CHROMA=true

# Start all services including ChromaDB
docker-compose up -d
```

### 2. Verify ChromaDB Connection
```bash
# Check ChromaDB service status
docker-compose ps chromadb

# Check ChromaDB logs
docker-compose logs chromadb

# Test ChromaDB API (should return heartbeat)
curl http://localhost:8001/api/v1/heartbeat
```

### 3. Monitor Vector Data
```bash
# Check ChromaDB collections
curl http://localhost:8001/api/v1/collections

# View collection details
curl http://localhost:8001/api/v1/collections/chatbot_collection
```

## Benefits of External ChromaDB

### 1. **Scalability**
- Dedicated vector database server
- Independent scaling of vector operations
- Better resource management

### 2. **Performance**
- Optimized for vector similarity search
- Faster retrieval operations
- Reduced memory usage on API server

### 3. **Data Persistence**
- Automatic data persistence
- No manual save operations required
- Consistent data across container restarts

### 4. **Monitoring & Management**
- Direct API access to ChromaDB
- Better observability
- Easier backup and maintenance

## Troubleshooting

### Connection Issues
If external ChromaDB fails, the system automatically falls back to local ChromaDB:

```bash
# Check API logs for ChromaDB connection
docker-compose logs api | grep -i chroma

# Common error messages:
# "Failed to connect to external ChromaDB" → Falls back to local
# "Connected to external ChromaDB" → Success
```

### Data Migration
To migrate from local to external ChromaDB:

1. **Export existing data** (if any):
   ```bash
   # Backup local vector store
   docker cp chatbot_support_api_1:/app/central_vector_store ./backup_vectors
   ```

2. **Switch to external ChromaDB**:
   ```bash
   # Update .env
   USE_EXTERNAL_CHROMA=true
   
   # Restart services
   docker-compose restart
   ```

3. **Re-upload documents** to populate external ChromaDB

## API Endpoints

### ChromaDB Direct Access
- **Health Check**: `http://localhost:8001/api/v1/heartbeat`
- **Collections**: `http://localhost:8001/api/v1/collections`
- **Admin UI**: `http://localhost:8001` (if available)

### Your Application API
- **Upload Documents**: `http://localhost:8000/upload`
- **Chat**: `http://localhost:8000/chat`
- **Vector Store Status**: Check logs for connection status

## Production Considerations

### 1. **Security**
- Configure ChromaDB authentication in production
- Use secure network connections
- Implement proper access controls

### 2. **Backup**
- Regular backup of `chromadb-data` volume
- Export collections periodically
- Test restore procedures

### 3. **Monitoring**
- Monitor ChromaDB performance metrics
- Set up alerts for connection failures
- Track vector store size and growth

## Summary

Your system now uses **external ChromaDB** for vector storage, providing:
- ✅ Dedicated vector database server
- ✅ Automatic data persistence
- ✅ Better scalability and performance
- ✅ Fallback to local storage if needed
- ✅ Easy monitoring and management

The vector data for your PDFs is stored in the external ChromaDB server, accessible at `http://localhost:8001`, with automatic persistence and no manual save operations required.
