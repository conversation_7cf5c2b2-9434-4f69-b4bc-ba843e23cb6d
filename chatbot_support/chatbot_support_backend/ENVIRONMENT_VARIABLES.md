# Environment Variables Guide

This document explains the environment variables used in the RAG Chatbot application for AWS deployment.

## Backend Environment Variables

### Database Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `DB_HOST` | Database host | `db` |
| `DB_PORT` | Database port | `3306` |
| `DB_USER` | Database username | `raguser` |
| `DB_PASSWORD` | Database password | `ragpassword` |
| `DB_NAME` | Database name | `chatbot` |
| `DATABASE_LOGGING` | Enable database query logging | `true` |
| `DATABASE_URL` | Full database connection string | `mysql+pymysql://raguser:ragpassword@db:3306/ragchatbot` |

### AWS S3 Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `AWS_ACCESS_KEY_ID` | AWS access key ID | - |
| `AWS_SECRET_ACCESS_KEY` | AWS secret access key | - |
| `AWS_REGION` | AWS region | `us-east-1` |
| `S3_BUCKET_NAME` | S3 bucket name for document storage | - |
| `S3_PREFIX` | Prefix for S3 object keys | `chatbot_support` |

### Vector Store Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `VECTOR_STORE_TYPE` | Type of vector store | `chroma` |
| `VECTOR_STORE_DIR` | Directory for vector store data | `/app/central_vector_store` |
| `USER_DATA_DIR` | Directory for user data | `/app/user_data` |

### ChromaDB Configuration (External Vector Database)

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `CHROMA_HOST` | ChromaDB server hostname | `chromadb` |
| `CHROMA_PORT` | ChromaDB server port | `8000` |
| `USE_EXTERNAL_CHROMA` | Whether to use external ChromaDB server | `true` |

### AI Model Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `DEFAULT_EMBEDDING_MODEL` | Default embedding model name | `all-MiniLM-L6-v2` |
| `DEFAULT_LLM_MODEL` | Default LLM model for Google AI | `gemini-pro` |
| `DEFAULT_OPENAI_MODEL` | Default OpenAI model | `gpt-3.5-turbo` |
| `LLM_TEMPERATURE` | Temperature setting for LLM responses | `0.1` |
| `USE_GOOGLE_LLM` | Whether to use Google AI for LLM by default | `true` |
| `USE_GOOGLE_EMBEDDINGS` | Whether to use Google AI for embeddings by default | `false` |
| `USE_OPENAI_EMBEDDINGS` | Whether to use OpenAI for embeddings by default | `false` |

### Document Processing Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `PDF_CHUNK_SIZE` | Size of text chunks for document processing | `1000` |
| `PDF_CHUNK_OVERLAP` | Overlap between consecutive chunks | `200` |
| `USE_COMPRESSION` | Whether to use contextual compression by default | `false` |
| `TOP_K_RETRIEVAL` | Number of documents to retrieve for context | `4` |
| `MAX_MEMORY_MESSAGES` | Maximum number of messages to store in memory | `10` |

### JWT Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `JWT_SECRET_KEY` | Secret key for JWT token signing | `your-secret-key-change-in-production` |
| `JWT_ALGORITHM` | Algorithm for JWT token signing | `HS256` |
| `JWT_ACCESS_TOKEN_EXPIRE_MINUTES` | JWT token expiration time in minutes | `30` |

### Google API Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `GOOGLE_SERVICE_ACCOUNT_FILE` | Path to Google service account JSON file | `/app/google_credentials.json` |
| `GOOGLE_PROJECT_ID` | Google Cloud project ID | - |
| `GOOGLE_API_KEY` | Google API key (alternative to service account) | - |

### OpenAI API Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `OPENAI_API_KEY` | OpenAI API key | - |

### CORS Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `CORS_ORIGINS` | Comma-separated list of allowed origins for CORS | `*` |

### Server Configuration

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `GUNICORN_WORKERS` | Number of Gunicorn worker processes | `4` |
| `GUNICORN_TIMEOUT` | Gunicorn timeout in seconds | `120` |

## Frontend Environment Variables

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `REACT_APP_API_URL` | URL for the frontend to connect to the backend API | `http://localhost:8000` |
| `REACT_APP_ENV` | Environment (development, production) | `production` |
| `REACT_APP_VERSION` | Application version | `1.0.0` |
| `BACKEND_API_URL` | URL for the Nginx proxy to connect to the backend API | `http://api:8000` |

## Setting Environment Variables

### For Local Development

1. Copy `.env.example` to `.env`
2. Update the values in `.env` according to your deployment environment
3. For Docker deployment, the environment variables will be automatically loaded from the `.env` file

### Production Deployment Notes

- **Security**: Always change `JWT_SECRET_KEY` in production
- **Performance**: Adjust `GUNICORN_WORKERS` based on your server capacity (typically 2-4 workers per CPU core)
- **AI Models**: Configure appropriate model names for your use case and available APIs
- **Chunk Sizes**: Tune `PDF_CHUNK_SIZE` and `PDF_CHUNK_OVERLAP` for optimal retrieval performance
- **Memory**: Adjust `MAX_MEMORY_MESSAGES` based on your memory constraints and conversation length requirements
- **Temperature**: Lower `LLM_TEMPERATURE` values (0.0-0.3) for more deterministic responses, higher values (0.7-1.0) for more creative responses

### For Local Development

Create a `.env` file in the root directory with the required variables:

```bash
# Database configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=raguser
DB_PASSWORD=ragpassword
DB_NAME=ragchatbot

# AWS S3 configuration
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
S3_BUCKET_NAME=your-bucket-name

# Vector store configuration
VECTOR_STORE_DIR=/app/central_vector_store

# JWT configuration
JWT_SECRET_KEY=your-secret-key

# Google API configuration
GOOGLE_PROJECT_ID=your-project-id

# Frontend configuration
REACT_APP_API_URL=http://localhost:8000
```

### For AWS Deployment

Set environment variables in your AWS environment:

1. **EC2 Instance**: Set environment variables in the `.env` file
2. **ECS/EKS**: Set environment variables in the task definition or deployment configuration
3. **Elastic Beanstalk**: Set environment variables in the environment configuration

## Using the Environment Variables

### In Docker Compose

The `docker-compose.yml` file is configured to use environment variables with fallback values:

```yaml
environment:
  - DB_HOST=${DB_HOST:-db}
  - DB_PORT=${DB_PORT:-3306}
  - DB_USER=${DB_USER:-raguser}
  - DB_PASSWORD=${DB_PASSWORD:-ragpassword}
  - DB_NAME=${DB_NAME:-ragchatbot}
```

### In the Frontend

The frontend uses a runtime configuration system that loads environment variables from `env-config.js`:

```javascript
// API base URL - Get from environment variables at runtime
const API_BASE_URL = window._env_ && window._env_.REACT_APP_API_URL
  ? window._env_.REACT_APP_API_URL
  : 'http://localhost:8000';
```

The `env.sh` script generates this file at container startup based on the environment variables.
