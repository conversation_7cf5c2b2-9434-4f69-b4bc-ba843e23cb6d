# RAG Chatbot

A production-ready RAG (Retrieval-Augmented Generation) chatbot for PDF documents with role-based access control.

## Features

- **PDF Document Processing**: Automatically process and index PDF documents
- **Role-Based Access Control**: Admin and user roles with document access control
- **User Management**: Create, update, and delete users and groups
- **Document Access Control**: Assign documents to specific groups
- **Conversational Memory**: Maintain conversation history
- **Modern UI**: Clean, responsive interface with markdown support
- **Cloud Storage**: Store documents in Amazon S3
- **Database Storage**: Store user data and embeddings in MySQL
- **Docker Support**: Easy deployment with Docker Compose

## Architecture

- **Frontend**: HTML, CSS, JavaScript
- **Backend**: FastAPI (Python)
- **Database**: MySQL
- **Storage**: Amazon S3
- **Embeddings**: Sentence Transformers / Google AI
- **LLM**: Google AI Studio API

## Prerequisites

- Docker and Docker Compose
- AWS account with S3 access
- Google Cloud account with AI Studio API access

## Environment Variables

Create a `.env` file in the project root with the following variables:

```
# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-s3-bucket-name

# JWT Configuration
JWT_SECRET_KEY=your-secret-key-change-in-production

# Database Configuration (optional - defaults are in docker-compose.yml)
MYSQL_ROOT_PASSWORD=rootpassword
MYSQL_DATABASE=ragchatbot
MYSQL_USER=raguser
MYSQL_PASSWORD=ragpassword
```

## Google Authentication

Place your Google service account JSON file at the project root as `google_credentials.json`.

## Deployment

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/ragchatbot.git
   cd ragchatbot
   ```

2. Create the `.env` file with your configuration.

3. Place your Google service account JSON file at the project root.

4. Build and start the containers:
   ```
   docker-compose up -d
   ```

5. Access the application:
   - Frontend: http://localhost
   - API: http://localhost:8000
   - Database Admin: http://localhost:8080

## Default Admin Account

- Username: admin
- Password: admin

**Important**: Change the default admin password after first login.

## Development

### Local Setup

1. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the API server:
   ```
   uvicorn ragchatbot.api.app:app --reload
   ```

4. Serve the frontend:
   ```
   cd frontend
   python -m http.server 8080
   ```

## License

MIT
