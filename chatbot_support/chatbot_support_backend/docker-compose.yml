version: '3.8'

services:
  # API service
  api:
    build: .
    restart: always
    ports:
      - "8000:8000"
    environment:
      - ENV=${ENV:-local}

      # Database configuration
      - DB_HOST=${DB_HOST:-db}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-raguser}
      - DB_PASSWORD=${DB_PASSWORD:-ragpassword}
      - DB_NAME=${DB_NAME:-ragchatbot}
      - DATABASE_LOGGING=${DATABASE_LOGGING:-true}

      # AWS S3 configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_PREFIX=${S3_PREFIX:-chatbot_support}
      - DB_SECRET_ARN=${DB_SECRET_ARN}

      # Vector store configuration
      - VECTOR_STORE_TYPE=${VECTOR_STORE_TYPE:-chroma}
      - VECTOR_STORE_DIR=${VECTOR_STORE_DIR:-/app/central_vector_store}
      - USER_DATA_DIR=${USER_DATA_DIR:-/app/user_data}

      # ChromaDB configuration (for dedicated ChromaDB server)
      - CHROMA_HOST=${CHROMA_HOST:-chromadb}
      - CHROMA_PORT=${CHROMA_PORT:-8000}
      - USE_EXTERNAL_CHROMA=${USE_EXTERNAL_CHROMA:-false}

      # AI Model configuration
      - DEFAULT_EMBEDDING_MODEL=${DEFAULT_EMBEDDING_MODEL:-all-MiniLM-L6-v2}
      - DEFAULT_LLM_MODEL=${DEFAULT_LLM_MODEL:-gemini-pro}
      - DEFAULT_OPENAI_MODEL=${DEFAULT_OPENAI_MODEL:-gpt-3.5-turbo}
      - LLM_TEMPERATURE=${LLM_TEMPERATURE:-0.1}
      - USE_GOOGLE_LLM=${USE_GOOGLE_LLM:-true}
      - USE_GOOGLE_EMBEDDINGS=${USE_GOOGLE_EMBEDDINGS:-false}
      - USE_OPENAI_EMBEDDINGS=${USE_OPENAI_EMBEDDINGS:-false}

      # Document processing configuration
      - PDF_CHUNK_SIZE=${PDF_CHUNK_SIZE:-1000}
      - PDF_CHUNK_OVERLAP=${PDF_CHUNK_OVERLAP:-200}
      - USE_COMPRESSION=${USE_COMPRESSION:-false}
      - TOP_K_RETRIEVAL=${TOP_K_RETRIEVAL:-4}
      - MAX_MEMORY_MESSAGES=${MAX_MEMORY_MESSAGES:-10}

      # JWT configuration
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-change-in-production}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=${JWT_ACCESS_TOKEN_EXPIRE_MINUTES:-30}

      # Google API configuration
      - GOOGLE_SERVICE_ACCOUNT_FILE=/app/google_credentials.json
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}

      # OpenAI API configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}

      # CORS configuration
      - CORS_ORIGINS=${CORS_ORIGINS:-*}

      # Server configuration
      - GUNICORN_WORKERS=${GUNICORN_WORKERS:-4}
      - GUNICORN_TIMEOUT=${GUNICORN_TIMEOUT:-120}

      # Other settings
      - PYTHONUNBUFFERED=1
    volumes:
      - ./google_credentials.json:/app/google_credentials.json:ro
      - data-volume:/app/data
      - vector-store-volume:/app/central_vector_store
      - user-data-volume:/app/user_data
    depends_on:
      db:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    networks:
      - ragchatbot-network
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/docs')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend service (React-based)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: always
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
      - REACT_APP_ENV=${REACT_APP_ENV:-production}
      - REACT_APP_VERSION=${REACT_APP_VERSION:-1.0.0}
      - BACKEND_API_URL=${BACKEND_API_URL:-http://api:8000}
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      api:
        condition: service_healthy
    networks:
      - ragchatbot-network

  # Database service
  db:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=ragchatbot
      - MYSQL_USER=raguser
      - MYSQL_PASSWORD=ragpassword
    volumes:
      - mysql-data:/var/lib/mysql
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "3306:3306"
    networks:
      - ragchatbot-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "raguser", "-pragpassword"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ChromaDB service (for dedicated vector database)
  chromadb:
    image: chromadb/chroma:latest
    restart: always
    ports:
      - "8001:8000"
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_PORT=8000
      - CHROMA_SERVER_CORS_ALLOW_ORIGINS=["*"]
    volumes:
      - chromadb-data:/chroma/chroma
    networks:
      - ragchatbot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Adminer for database management (optional)
  adminer:
    image: adminer
    restart: always
    ports:
      - "8081:8080"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - ragchatbot-network

networks:
  ragchatbot-network:
    driver: bridge

volumes:
  data-volume:
    driver: local
  user-data-volume:
    driver: local
  vector-store-volume:
    driver: local
  mysql-data:
    driver: local
  chromadb-data:
    driver: local
