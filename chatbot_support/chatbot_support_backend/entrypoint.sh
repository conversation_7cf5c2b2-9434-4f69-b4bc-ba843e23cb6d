#!/bin/bash
set -e

# Wait for the database to be ready
echo "Waiting for database to be ready..."
python -c "
import time
import pymysql
while True:
    try:
        pymysql.connect(
            host='db',
            user='raguser',
            password='ragpassword',
            database='ragchatbot'
        )
        break
    except pymysql.Error:
        print('Database not ready yet. Waiting...')
        time.sleep(1)
"
echo "Database is ready!"

# Initialize the database
echo "Initializing database..."
python -c "
from ragchatbot.database.db import init_db
init_db()
"
echo "Database initialized!"

# Run database migrations
echo "Running database migrations..."
python run_migrations.py
echo "Database migrations completed!"

# Start the application
echo "Starting application..."
WORKERS=${GUNICORN_WORKERS:-4}
TIMEOUT=${GUNICORN_TIMEOUT:-120}
exec gunicorn -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 --workers $WORKERS --timeout $TIMEOUT ragchatbot.api.app:app
