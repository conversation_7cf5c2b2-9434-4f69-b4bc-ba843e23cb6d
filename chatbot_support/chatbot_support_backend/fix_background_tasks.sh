#!/bin/bash

# This script fixes the BackgroundTasks parameter in the upload endpoint

echo "Fixing BackgroundTasks parameter in app.py..."

# Create a backup of the original file
cp /home/<USER>/Workspace/chatbot_vibe/chatbot_support/ragchatbot/api/app.py /home/<USER>/Workspace/chatbot_vibe/chatbot_support/ragchatbot/api/app.py.bak

# Replace the upload endpoint definition
sed -i 's/async def upload_pdf(\n    file: UploadFile = File(..., description="The PDF file to upload"),\n    groups: List\[str\] = Form(\[\], description="List of group IDs with access to the document"),\n    background_tasks: BackgroundTasks = Depends(),\n    current_user: dict = Depends(get_current_admin)  # Only admins can upload documents/async def upload_pdf(\n    background_tasks: BackgroundTasks,\n    file: UploadFile = File(..., description="The PDF file to upload"),\n    groups: List\[str\] = Form(\[\], description="List of group IDs with access to the document"),\n    current_user: dict = Depends(get_current_admin)  # Only admins can upload documents/g' /home/<USER>/Workspace/chatbot_vibe/chatbot_support/ragchatbot/api/app.py

echo "Fix applied successfully!"
echo "Restart the API server to apply the changes."
