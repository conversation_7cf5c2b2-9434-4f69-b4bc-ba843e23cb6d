#!/bin/bash

# This script installs the enhanced hard reset functionality for the chatbot

echo "Installing enhanced hard reset functionality..."

# Create the hard_reset.py file if it doesn't exist
mkdir -p chatbot_support/ragchatbot/api
cp chatbot_support/ragchatbot/api/hard_reset.py chatbot_support/ragchatbot/api/hard_reset.py.bak
echo "Backed up original hard_reset.py file"

# Restart the backend server
echo "Restarting backend server..."
pkill -f "python3 -m ragchatbot.api.app"
cd chatbot_support && python3 -m ragchatbot.api.app &

echo "Enhanced hard reset functionality installed successfully!"
echo "The hard reset feature will now completely remove all documents, embeddings, and vector stores."
echo "To use it, go to Admin Settings and click the 'Reset System' button."
