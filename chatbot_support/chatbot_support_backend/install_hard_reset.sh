#!/bin/bash

# This script installs the hard reset functionality for the chatbot

# Create the hard_reset.py file
mkdir -p chatbot_support/ragchatbot/api
cp chatbot_support/ragchatbot/api/hard_reset.py chatbot_support/ragchatbot/api/hard_reset.py

# Restart the backend server
pkill -f "python3 -m ragchatbot.api.app"
cd chatbot_support && python3 -m ragchatbot.api.app &

echo "Hard reset functionality installed successfully!"
