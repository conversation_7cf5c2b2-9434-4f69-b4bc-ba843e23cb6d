"""
Hard reset functionality for the RAG chatbot.
"""

import os
import logging
import shutil
import glob
from pathlib import Path
from typing import List, Dict, Any, Optional

from sqlalchemy.orm import Session
from sqlalchemy import delete, text

from ragchatbot.database.db import get_db
from ragchatbot.database.models import Document, Embedding, DocumentAccess, ChatbotConfig
from ragchatbot.storage.s3 import S3Storage

logger = logging.getLogger(__name__)

def hard_reset_system() -> Dict[str, Any]:
    """
    Perform a hard reset of the system.

    This will:
    1. Delete all documents from the database
    2. Delete all embeddings from the database
    3. Delete all document access records
    4. Delete all chatbot configurations
    5. Delete all files from S3
    6. Delete any local vector store directories
    7. Delete user data directories
    8. Delete data directory contents

    Returns:
        A dictionary with the results of the operation
    """
    results = {
        "success": True,
        "documents_deleted": 0,
        "embeddings_deleted": 0,
        "document_access_deleted": 0,
        "chatbot_configs_deleted": 0,
        "s3_files_deleted": 0,
        "vector_stores_deleted": False,
        "user_data_deleted": False,
        "data_dir_cleaned": False,
        "errors": []
    }

    try:
        # 1. Get all document S3 keys before deleting from database
        s3_keys = []
        with get_db() as db:
            documents = db.query(Document).all()
            results["documents_deleted"] = len(documents)

            # Collect all S3 keys
            for doc in documents:
                s3_keys.append(doc.s3_key)

        # 2. Delete all database records
        with get_db() as db:
            try:
                # Delete document access records
                access_count = db.query(DocumentAccess).count()
                results["document_access_deleted"] = access_count
                db.execute(delete(DocumentAccess))

                # Delete embeddings (they have foreign key constraints)
                embedding_count = db.query(Embedding).count()
                results["embeddings_deleted"] = embedding_count
                db.execute(delete(Embedding))

                # Delete all documents
                db.execute(delete(Document))

                # Delete all chatbot configs
                config_count = db.query(ChatbotConfig).count()
                results["chatbot_configs_deleted"] = config_count
                db.execute(delete(ChatbotConfig))

                # Commit the changes
                db.commit()

                # Reset auto-increment counters if using SQLite
                try:
                    db.execute(text("DELETE FROM sqlite_sequence WHERE name IN ('documents', 'embeddings', 'document_access', 'chatbot_configs')"))
                    db.commit()
                except Exception as e:
                    # This is not critical, so just log it
                    logger.warning(f"Could not reset SQLite sequence: {str(e)}")

            except Exception as e:
                logger.error(f"Error deleting database records: {str(e)}")
                results["errors"].append(f"Failed to delete database records: {str(e)}")
                db.rollback()

        # 3. Delete files from S3
        try:
            s3_storage = S3Storage()

            # Try to delete all files with the documents prefix
            try:
                all_files = s3_storage.list_files("documents/")
                for file_info in all_files:
                    try:
                        s3_storage.delete_file(file_info['key'])
                        results["s3_files_deleted"] += 1
                    except Exception as e:
                        logger.error(f"Error deleting file from S3: {file_info['key']} - {str(e)}")
                        results["errors"].append(f"Failed to delete S3 file: {file_info['key']} - {str(e)}")
            except Exception as e:
                logger.error(f"Error listing S3 files: {str(e)}")
                results["errors"].append(f"Failed to list S3 files: {str(e)}")

            # Also try to delete the specific keys we collected
            for s3_key in s3_keys:
                if s3_key and not s3_key.startswith('local/'):  # Skip local files
                    try:
                        s3_storage.delete_file(s3_key)
                        results["s3_files_deleted"] += 1
                    except Exception as e:
                        logger.error(f"Error deleting file from S3: {s3_key} - {str(e)}")
                        results["errors"].append(f"Failed to delete S3 file: {s3_key} - {str(e)}")
        except Exception as e:
            logger.error(f"Error initializing S3 storage: {str(e)}")
            results["errors"].append(f"Failed to initialize S3 storage: {str(e)}")

        # 4. Delete local vector store directories
        vector_store_dirs = [
            "vector_store",
            "chroma_db",
            "faiss_index"
        ]

        for dir_path in vector_store_dirs:
            if os.path.exists(dir_path):
                try:
                    shutil.rmtree(dir_path)
                    results["vector_stores_deleted"] = True
                except Exception as e:
                    logger.error(f"Error deleting vector store directory {dir_path}: {str(e)}")
                    results["errors"].append(f"Failed to delete vector store directory: {dir_path} - {str(e)}")

        # 5. Delete user_data directory
        user_data_dir = Path("user_data")
        if user_data_dir.exists():
            try:
                shutil.rmtree(user_data_dir)
                results["user_data_deleted"] = True
            except Exception as e:
                logger.error(f"Error deleting user_data directory: {str(e)}")
                results["errors"].append(f"Failed to delete user_data directory: {str(e)}")

        # 6. Clean data directory
        data_dir = Path("data")
        if data_dir.exists():
            try:
                # Remove all files in the data directory
                for file_path in data_dir.glob("**/*"):
                    if file_path.is_file():
                        file_path.unlink()
                results["data_dir_cleaned"] = True
            except Exception as e:
                logger.error(f"Error cleaning data directory: {str(e)}")
                results["errors"].append(f"Failed to clean data directory: {str(e)}")

        # 7. Look for any chroma directories and delete them
        try:
            # Find all chroma directories in the current directory and subdirectories
            for chroma_dir in Path('.').glob('**/chroma'):
                if chroma_dir.is_dir():
                    try:
                        shutil.rmtree(chroma_dir)
                        results["vector_stores_deleted"] = True
                    except Exception as e:
                        logger.error(f"Error deleting chroma directory {chroma_dir}: {str(e)}")
                        results["errors"].append(f"Failed to delete chroma directory: {chroma_dir} - {str(e)}")
        except Exception as e:
            logger.error(f"Error finding chroma directories: {str(e)}")
            results["errors"].append(f"Failed to find chroma directories: {str(e)}")

        # 8. Specifically target user_data directory and all vector stores
        try:
            # Find all vector_store directories in user_data
            user_data_path = Path("user_data")
            if user_data_path.exists():
                for vector_store_dir in user_data_path.glob('**/vector_store'):
                    if vector_store_dir.is_dir():
                        try:
                            shutil.rmtree(vector_store_dir)
                            results["vector_stores_deleted"] = True
                        except Exception as e:
                            logger.error(f"Error deleting vector store directory {vector_store_dir}: {str(e)}")
                            results["errors"].append(f"Failed to delete vector store directory: {vector_store_dir} - {str(e)}")
        except Exception as e:
            logger.error(f"Error finding vector store directories in user_data: {str(e)}")
            results["errors"].append(f"Failed to find vector store directories in user_data: {str(e)}")

        # 9. Clear the chatbot storage in memory
        try:
            from ragchatbot.storage.chatbot_storage import ChatbotStorage
            chatbot_storage = ChatbotStorage()
            chatbot_storage.clear_all()
            logger.info("Cleared all chatbot instances from memory")
        except Exception as e:
            logger.error(f"Error clearing chatbot storage: {str(e)}")
            results["errors"].append(f"Failed to clear chatbot storage: {str(e)}")

        return results

    except Exception as e:
        logger.error(f"Error performing hard reset: {str(e)}")
        results["success"] = False
        results["errors"].append(f"General error: {str(e)}")
        return results
