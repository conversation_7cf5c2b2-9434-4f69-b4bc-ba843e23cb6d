"""
PDF processing module for extracting and chunking text from PDF documents.
"""

import os
import tempfile
import uuid
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

from langchain_community.document_loaders import PyPDFLoader, UnstructuredPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

from ..storage.s3 import S3Storage

logger = logging.getLogger(__name__)

# Get environment variables for PDF processing
def get_env_var(var_name, default_value):
    """Get environment variable with a default value"""
    return os.environ.get(var_name, default_value)

DEFAULT_CHUNK_SIZE = int(get_env_var("PDF_CHUNK_SIZE", "1000"))
DEFAULT_CHUNK_OVERLAP = int(get_env_var("PDF_CHUNK_OVERLAP", "200"))

class PDFProcessor:
    """
    Handles the extraction and processing of text from PDF documents.
    """

    def __init__(
        self,
        chunk_size: int = DEFAULT_CHUNK_SIZE,
        chunk_overlap: int = DEFAULT_CHUNK_OVERLAP,
        use_unstructured: bool = False,
        s3_storage: Optional[S3Storage] = None
    ):
        """
        Initialize the PDF processor.

        Args:
            chunk_size: The size of text chunks to create
            chunk_overlap: The overlap between consecutive chunks
            use_unstructured: Whether to use UnstructuredPDFLoader (better for complex PDFs)
            s3_storage: Optional S3Storage instance
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.use_unstructured = use_unstructured
        self.extract_images = False  # Always disable image extraction
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
        )

        # Initialize S3 storage if not provided
        self.s3_storage = s3_storage or S3Storage()

    def process_pdf(self, pdf_path: str, s3_key: Optional[str] = None, document_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Process a single PDF file, either from local path or S3.

        Args:
            pdf_path: Path to the PDF file or identifier
            s3_key: Optional S3 key if the file is in S3
            document_id: Optional document ID for image extraction

        Returns:
            List of document chunks with metadata
        """
        temp_file = None
        actual_path = pdf_path

        try:
            # If S3 key is provided, download the file to a temporary location
            if s3_key and s3_key.startswith("chatbot_support/"):
                logger.info(f"Downloading PDF from S3: {s3_key}")
                try:
                    temp_file_path = self.s3_storage.download_file(s3_key)
                    temp_file = temp_file_path  # Store for cleanup
                    actual_path = temp_file_path
                    logger.info(f"Downloaded S3 file to temporary location: {temp_file_path}")
                except Exception as s3_error:
                    logger.error(f"Error downloading from S3, falling back to local path: {str(s3_error)}")

            logger.info(f"Processing PDF: {actual_path}")

            # Generate a document ID if not provided
            if not document_id:
                document_id = str(uuid.uuid4())

            # Choose the appropriate loader based on configuration
            if self.use_unstructured:
                loader = UnstructuredPDFLoader(actual_path)
            else:
                loader = PyPDFLoader(actual_path)

            # Load the document
            try:
                documents = loader.load()
            except Exception as load_error:
                logger.error(f"Error loading document {pdf_path}: {str(load_error)}")
                return []

            # Add source metadata
            for doc in documents:
                doc.metadata["source"] = pdf_path
                doc.metadata["file_name"] = os.path.basename(pdf_path)
                doc.metadata["document_id"] = document_id
                if s3_key:
                    doc.metadata["s3_key"] = s3_key

                # Set image-related metadata to default values
                doc.metadata["has_images"] = False
                doc.metadata["image_count"] = 0
                doc.metadata["image_references"] = ""

            # Split the documents into chunks
            chunked_documents = self.text_splitter.split_documents(documents)

            # Set default image metadata for all chunks
            for chunk in chunked_documents:
                # Ensure all chunks have consistent metadata fields
                chunk.metadata["has_images"] = False
                chunk.metadata["image_count"] = 0
                chunk.metadata["image_references"] = ""

            logger.info(f"Extracted {len(chunked_documents)} chunks from {pdf_path}")
            return chunked_documents

        except Exception as e:
            logger.error(f"Error processing PDF {pdf_path}: {str(e)}")
            return []
        finally:
            # Clean up temporary file if it was created
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    logger.info(f"Removed temporary file: {temp_file}")
                except Exception as cleanup_error:
                    logger.error(f"Error removing temporary file: {str(cleanup_error)}")

    def process_directory(self, directory_path: str, recursive: bool = True, use_s3: bool = False) -> List[Dict[str, Any]]:
        """
        Process all PDF files in a directory.

        Args:
            directory_path: Path to the directory containing PDF files
            recursive: Whether to recursively process subdirectories
            use_s3: Whether to check for S3 keys in the database

        Returns:
            List of document chunks with metadata
        """
        logger.info(f"Processing directory: {directory_path}")
        all_documents = []

        # Get all PDF files in the directory
        pdf_files = []
        directory = Path(directory_path)

        if recursive:
            pdf_files = list(directory.glob('**/*.pdf'))
        else:
            pdf_files = list(directory.glob('*.pdf'))

        logger.info(f"Found {len(pdf_files)} PDF files in {directory_path}")

        # If use_s3 is True, check for S3 keys in the database
        s3_keys = {}
        if use_s3:
            try:
                from ..database.db import get_db
                from ..database.models import Document as DBDocument

                with get_db() as db:
                    # Get all documents from the database
                    documents = db.query(DBDocument).all()
                    # Create a mapping of filename to S3 key
                    s3_keys = {doc.filename: doc.s3_key for doc in documents if doc.s3_key and not doc.s3_key.startswith("local/")}
                    logger.info(f"Found {len(s3_keys)} S3 keys in the database")
            except Exception as e:
                logger.error(f"Error getting S3 keys from database: {str(e)}")

        # Process each PDF file
        for pdf_file in pdf_files:
            filename = pdf_file.name
            s3_key = s3_keys.get(filename)

            # Get document ID from database if available
            document_id = None
            if use_s3:
                try:
                    from ..database.db import get_db
                    from ..database.models import Document as DBDocument

                    with get_db() as db:
                        doc = db.query(DBDocument).filter(DBDocument.filename == filename).first()
                        if doc:
                            document_id = doc.id
                            logger.info(f"Found document ID {document_id} for file {filename}")
                except Exception as e:
                    logger.error(f"Error getting document ID from database: {str(e)}")

            if s3_key:
                logger.info(f"Using S3 key {s3_key} for file {filename}")
                documents = self.process_pdf(str(pdf_file), s3_key=s3_key, document_id=document_id)
            else:
                documents = self.process_pdf(str(pdf_file), document_id=document_id)

            all_documents.extend(documents)

        return all_documents
