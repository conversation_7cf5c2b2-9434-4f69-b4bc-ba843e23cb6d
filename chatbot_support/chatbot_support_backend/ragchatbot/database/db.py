"""
Database connection module for the RAG chatbot.
"""

import os
import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from ragchatbot.utils import secrets_manager as sm

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get database URL from environment variable or use MySQL as default
# For Docker: mysql+pymysql://raguser:ragpassword@db:3306/ragchatbot
# For local development: mysql+pymysql://raguser:ragpassword@localhost:3306/ragchatbot

db_creds = sm.get_db_credentials()
DATABASE_URL = f"mysql+pymysql://{db_creds['user']}:{db_creds['password']}@{db_creds['host']}:{db_creds['port']}/{db_creds['database']}"

# Enable database logging if requested
DATABASE_LOGGING = os.getenv("DATABASE_LOGGING", "false").lower() == "true"

# Create SQLAlchemy engine
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Check connection before using it
    pool_recycle=3600,   # Recycle connections after 1 hour
    echo=DATABASE_LOGGING  # Enable SQL query logging if requested
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

class DatabaseSession:
    """
    Database session context manager.
    """

    def __init__(self):
        self.db = None

    def __enter__(self):
        self.db = SessionLocal()
        return self.db

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            logger.error(f"Error in database session: {str(exc_val)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.db.rollback()
        self.db.close()

def get_db():
    """
    Get a database session as a context manager.

    Usage:
        with get_db() as db:
            # Use db here

    Returns:
        A SQLAlchemy session context manager
    """
    return DatabaseSession()

def init_db():
    """
    Initialize the database by creating all tables.
    """
    try:
        # Import all models to ensure they are registered with Base
        from .models import User, Group, UserGroup, Document, DocumentAccess, Embedding, Conversation, Message

        # Create tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise
