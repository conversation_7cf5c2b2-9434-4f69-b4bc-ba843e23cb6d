"""
Database migration script to add document_number field to documents table.

This script adds a document_number column to the documents table and
generates unique document numbers for existing documents.
"""

import os
import sys
import logging
from sqlalchemy import text

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '../../..'))
sys.path.insert(0, parent_dir)

# Now we can import from ragchatbot
from ragchatbot.utils.document_number import get_unique_document_number
from ragchatbot.database.models import Document
from ragchatbot.database.db import engine, SessionLocal

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def upgrade():
    """
    Upgrade the database schema by adding the document_number column to the documents table
    and generating unique document numbers for existing documents.
    """
    try:
        # Add the document_number column
        logger.info("Adding document_number column to documents table...")
        with engine.connect() as conn:
            conn.execute(text("ALTER TABLE documents ADD COLUMN IF NOT EXISTS document_number VARCHAR(20) UNIQUE"))
            conn.commit()

        # Generate document numbers for existing documents
        logger.info("Generating document numbers for existing documents...")
        Session = SessionLocal()
        try:
            documents = Session.query(Document).all()
            logger.info(f"Found {len(documents)} documents to update")

            for document in documents:
                if not document.document_number:
                    document_number = get_unique_document_number(Session, prefix="DOC")
                    document.document_number = document_number
                    logger.info(f"Assigned document number {document_number} to document {document.id}")

            Session.commit()
            logger.info("Document numbers generated successfully")
        finally:
            Session.close()

        logger.info("Migration completed successfully")
    except Exception as e:
        logger.error(f"Error during migration: {str(e)}")
        raise


def downgrade():
    """
    Downgrade the database schema by removing the document_number column from the documents table.
    """
    try:
        logger.info("Removing document_number column from documents table...")
        with engine.connect() as conn:
            conn.execute(text("ALTER TABLE documents DROP COLUMN IF EXISTS document_number"))
            conn.commit()

        logger.info("Downgrade completed successfully")
    except Exception as e:
        logger.error(f"Error during downgrade: {str(e)}")
        raise


if __name__ == "__main__":
    # If script is run directly, perform the upgrade
    logger.info("Starting migration to add document_number field to documents table")
    upgrade()
    logger.info("Migration completed")
