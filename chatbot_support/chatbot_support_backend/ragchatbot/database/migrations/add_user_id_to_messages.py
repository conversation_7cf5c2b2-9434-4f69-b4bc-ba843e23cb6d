"""
Database migration script to add user_id column to messages table.
"""

import logging
import os
import sys

# Add the parent directory to the path so we can import the database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from sqlalchemy import text

from ragchatbot.database.db import get_db, engine
from ragchatbot.database.models import Message

logger = logging.getLogger(__name__)

def run_migration():
    """
    Run the migration to add user_id column to messages table.
    """
    try:
        # Check if the column already exists
        with get_db() as db:
            # Try to get a message and access the user_id attribute
            try:
                message = db.query(Message).first()
                if message and hasattr(message, 'user_id'):
                    logger.info("user_id column already exists in messages table")
                    return
            except Exception:
                # Column doesn't exist, continue with migration
                pass

        # Add the column - MySQL syntax
        with engine.connect() as connection:
            # Check if the column already exists in MySQL
            result = connection.execute(text("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'ragchatbot' AND table_name = 'messages' AND column_name = 'user_id'"))
            column_exists = result.scalar() > 0

            if not column_exists:
                connection.execute(text('ALTER TABLE messages ADD COLUMN user_id VARCHAR(36)'))
                connection.execute(text('ALTER TABLE messages ADD CONSTRAINT fk_messages_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE'))

        logger.info("Successfully added user_id column to messages table")

        # Update existing messages to set user_id from their conversation's user_id
        with get_db() as db:
            # Use a direct SQL update for better performance
            db.execute(text("""
                UPDATE messages m
                JOIN conversations c ON m.conversation_id = c.id
                SET m.user_id = c.user_id
                WHERE m.role = 'user'
            """))

            # Commit the changes
            db.commit()

        logger.info("Updated existing messages with user_id")

    except Exception as e:
        logger.error(f"Error running migration: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run the migration
    run_migration()
