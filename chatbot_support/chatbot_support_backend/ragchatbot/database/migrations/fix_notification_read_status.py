"""
Database migration script to fix notification_read_status table.
"""

import logging
import os
import sys
from sqlalchemy import text, Column, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import the database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from ragchatbot.database.db import get_db, engine

logger = logging.getLogger(__name__)
Base = declarative_base()

class NotificationReadStatus(Base):
    """Tracks which users have read which notifications."""
    __tablename__ = "notification_read_status"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    notification_id = Column(String(36), ForeignKey("notifications.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    read_at = Column(DateTime, default=datetime.utcnow)

    # Add a unique constraint to ensure a user can only mark a notification as read once
    __table_args__ = (
        UniqueConstraint('notification_id', 'user_id', name='uix_notification_user'),
    )

def run_migration():
    """
    Run the migration to ensure notification_read_status table exists.
    """
    try:
        # Check if the table already exists
        with engine.connect() as connection:
            # Check if the table exists in MySQL
            result = connection.execute(text("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'ragchatbot' AND table_name = 'notification_read_status'"))
            table_exists = result.scalar() > 0
            
            if table_exists:
                logger.info("notification_read_status table already exists")
                return

        # Create the table
        NotificationReadStatus.__table__.create(engine, checkfirst=True)
        logger.info("Successfully created notification_read_status table")
        
    except Exception as e:
        logger.error(f"Error running migration: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the migration
    run_migration()
