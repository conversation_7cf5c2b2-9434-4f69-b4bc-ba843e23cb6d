"""
Database migration script to ensure notifications table exists.
"""

import logging
import os
import sys
from sqlalchemy import text, Column, String, DateTime, ForeignKey, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import the database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from ragchatbot.database.db import get_db, engine

logger = logging.getLogger(__name__)
Base = declarative_base()

class Notification(Base):
    """Notification model for system events."""
    __tablename__ = "notifications"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=True)  # Target user (null for all users)
    created_by = Column(String(36), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)  # User who triggered the notification
    document_id = Column(String(36), ForeignKey("documents.id", ondelete="SET NULL"), nullable=True)  # Related document if any
    type = Column(String(50), nullable=False)  # Type of notification: 'document_added', 'document_deleted', 'user_added', etc.
    title = Column(String(255), nullable=False)  # Short title for the notification
    message = Column(Text, nullable=False)  # Detailed message
    is_read = Column(Boolean, default=False)  # Whether the notification has been read
    is_global = Column(Boolean, default=False)  # Whether this is a global notification for all users
    created_at = Column(DateTime, default=datetime.utcnow)

def run_migration():
    """
    Run the migration to ensure notifications table exists.
    """
    try:
        # Check if the table already exists
        with engine.connect() as connection:
            # Check if the table exists in MySQL
            result = connection.execute(text("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'ragchatbot' AND table_name = 'notifications'"))
            table_exists = result.scalar() > 0
            
            if table_exists:
                logger.info("notifications table already exists")
                return

        # Create the table
        Notification.__table__.create(engine, checkfirst=True)
        logger.info("Successfully created notifications table")
        
    except Exception as e:
        logger.error(f"Error running migration: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the migration
    run_migration()
