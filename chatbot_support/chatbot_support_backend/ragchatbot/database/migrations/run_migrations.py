"""
Script to run all database migrations.
"""

import logging
import os
import importlib.util
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def run_migrations():
    """
    Run all migration scripts in the migrations directory.
    """
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Get all Python files in the directory
    migration_files = [f for f in os.listdir(current_dir) if f.endswith('.py') and f != 'run_migrations.py' and f != '__init__.py']
    
    # Sort the files to ensure they run in order
    migration_files.sort()
    
    logger.info(f"Found {len(migration_files)} migration files: {migration_files}")
    
    # Run each migration
    for migration_file in migration_files:
        try:
            logger.info(f"Running migration: {migration_file}")
            
            # Load the module
            module_path = os.path.join(current_dir, migration_file)
            module_name = os.path.splitext(migration_file)[0]
            
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Run the migration
            if hasattr(module, 'run_migration'):
                module.run_migration()
                logger.info(f"Successfully ran migration: {migration_file}")
            else:
                logger.warning(f"Migration file {migration_file} does not have a run_migration function")
                
        except Exception as e:
            logger.error(f"Error running migration {migration_file}: {str(e)}")
            # Continue with the next migration

if __name__ == "__main__":
    run_migrations()
