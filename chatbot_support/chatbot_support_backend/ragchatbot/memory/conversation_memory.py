"""
Conversation memory for maintaining context across interactions.
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from ragchatbot.database.db import get_db
from ragchatbot.database.models import Conversation as DBConversation, Message as DBMessage

logger = logging.getLogger(__name__)

class Message:
    """
    Represents a single message in a conversation.
    """

    def __init__(self, content: str, role: str, timestamp: Optional[datetime] = None):
        """
        Initialize a message.

        Args:
            content: The message content
            role: The role of the sender ('user' or 'assistant')
            timestamp: When the message was sent
        """
        self.content = content
        self.role = role
        self.timestamp = timestamp or datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the message to a dictionary.

        Returns:
            Dictionary representation of the message
        """
        return {
            "content": self.content,
            "role": self.role,
            "timestamp": self.timestamp.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """
        Create a message from a dictionary.

        Args:
            data: Dictionary representation of a message

        Returns:
            Message instance
        """
        timestamp = datetime.fromisoformat(data["timestamp"]) if "timestamp" in data else None
        return cls(
            content=data["content"],
            role=data["role"],
            timestamp=timestamp
        )


class ConversationMemory:
    """
    Manages conversation history and context.
    """

    def __init__(self, max_messages: int = 10, user_id: Optional[str] = None, conversation_id: Optional[str] = None):
        """
        Initialize conversation memory.

        Args:
            max_messages: Maximum number of messages to store
            user_id: The ID of the user who owns this conversation
            conversation_id: The ID of an existing conversation to load
        """
        self.messages: List[Message] = []
        self.max_messages = max_messages
        self.user_id = user_id
        self.conversation_id = conversation_id

        # Load conversation from database if conversation_id is provided
        if conversation_id and user_id:
            self._load_from_database()
        elif user_id:
            # Create a new conversation in the database
            self._create_conversation()

        logger.info(f"Initialized conversation memory with max_messages={max_messages}")

    def _create_conversation(self) -> None:
        """
        Create a new conversation in the database.
        """
        if not self.user_id:
            logger.warning("Cannot create conversation without user_id")
            return

        try:
            with get_db() as db:
                # Create new conversation
                conversation = DBConversation(
                    id=str(uuid.uuid4()),
                    user_id=self.user_id
                )
                db.add(conversation)
                db.commit()
                db.refresh(conversation)

                # Set conversation ID
                self.conversation_id = conversation.id
                logger.info(f"Created new conversation with ID: {self.conversation_id}")
        except Exception as e:
            logger.error(f"Error creating conversation: {str(e)}")

    def _load_from_database(self) -> None:
        """
        Load conversation from the database.
        """
        if not self.conversation_id or not self.user_id:
            logger.warning("Cannot load conversation without conversation_id and user_id")
            return

        try:
            with get_db() as db:
                # Get conversation
                conversation = db.query(DBConversation).filter(
                    DBConversation.id == self.conversation_id,
                    DBConversation.user_id == self.user_id
                ).first()

                if not conversation:
                    logger.warning(f"Conversation not found: {self.conversation_id}")
                    return

                # Get messages
                db_messages = conversation.messages

                # Convert to Message objects
                self.messages = [
                    Message(
                        content=msg.content,
                        role=msg.role,
                        timestamp=msg.created_at
                    ) for msg in db_messages
                ]

                logger.info(f"Loaded conversation with {len(self.messages)} messages")
        except Exception as e:
            logger.error(f"Error loading conversation: {str(e)}")

    def add_user_message(self, content: str) -> None:
        """
        Add a user message to the conversation.

        Args:
            content: The message content
        """
        message = Message(content=content, role="user")
        self.messages.append(message)
        self._trim_messages()

        # Save to database if conversation_id exists
        if self.conversation_id:
            self._save_message_to_database(message)

        logger.debug(f"Added user message: {content}")

    def add_assistant_message(self, content: str) -> None:
        """
        Add an assistant message to the conversation.

        Args:
            content: The message content
        """
        message = Message(content=content, role="assistant")
        self.messages.append(message)
        self._trim_messages()

        # Save to database if conversation_id exists
        if self.conversation_id:
            self._save_message_to_database(message)

        logger.debug(f"Added assistant message: {content}")

    def _trim_messages(self) -> None:
        """
        Trim the message history to the maximum length.
        """
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]

    def _save_message_to_database(self, message: Message) -> None:
        """
        Save a message to the database.

        Args:
            message: The message to save
        """
        if not self.conversation_id:
            logger.warning("Cannot save message without conversation_id")
            return

        try:
            with get_db() as db:
                # Create new message
                db_message = DBMessage(
                    id=str(uuid.uuid4()),
                    conversation_id=self.conversation_id,
                    user_id=self.user_id,  # Add user_id to the message
                    role=message.role,
                    content=message.content
                )
                db.add(db_message)
                db.commit()
                logger.debug(f"Saved message to database: {message.content[:50]}...")
        except Exception as e:
            logger.error(f"Error saving message to database: {str(e)}")

    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        Get the conversation history as a list of dictionaries.

        Returns:
            List of message dictionaries
        """
        return [message.to_dict() for message in self.messages]

    def get_formatted_history(self, include_timestamps: bool = False, format_type: str = "default") -> str:
        """
        Get the conversation history as a formatted string.

        Args:
            include_timestamps: Whether to include timestamps
            format_type: The format type to use ("default", "gemini", "openai")

        Returns:
            Formatted conversation history
        """
        formatted_history = []

        # If there are no messages, return an empty string
        if not self.messages:
            return ""

        # Get message count for reference
        message_count = len(self.messages)

        if format_type == "gemini":
            # Enhanced format for Gemini model with message numbers for better reference
            formatted_history.append(f"Conversation history ({message_count} messages):")

            for i, message in enumerate(self.messages):
                role = "User" if message.role == "user" else "Assistant"
                msg_num = i + 1

                if include_timestamps:
                    timestamp_str = message.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    formatted_message = f"[{msg_num}/{message_count}] {role}: {message.content} [Time: {timestamp_str}]"
                else:
                    formatted_message = f"[{msg_num}/{message_count}] {role}: {message.content}"
                formatted_history.append(formatted_message)

            return "\n".join(formatted_history)

        elif format_type == "openai":
            # Enhanced format for OpenAI model
            formatted_history.append(f"Conversation history ({message_count} messages):")

            for i, message in enumerate(self.messages):
                role = message.role.capitalize()
                msg_num = i + 1

                if include_timestamps:
                    timestamp_str = message.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    formatted_message = f"[{msg_num}/{message_count}] {role}: {message.content} [Time: {timestamp_str}]"
                else:
                    formatted_message = f"[{msg_num}/{message_count}] {role}: {message.content}"
                formatted_history.append(formatted_message)

            return "\n".join(formatted_history)

        else:
            # Enhanced default format with better structure
            formatted_history.append(f"Conversation history ({message_count} messages):")

            for i, message in enumerate(self.messages):
                role = message.role.capitalize()
                msg_num = i + 1

                if include_timestamps:
                    timestamp_str = message.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    formatted_message = f"[{msg_num}/{message_count}] {role} ({timestamp_str}):\n{message.content}"
                else:
                    formatted_message = f"[{msg_num}/{message_count}] {role}:\n{message.content}"
                formatted_history.append(formatted_message)

            return "\n\n".join(formatted_history)

    def get_conversation_summary(self, max_length: int = 200) -> str:
        """
        Generate a summary of the conversation.

        This method creates a concise summary of the conversation history,
        which can be useful for providing context without using the full history.

        Args:
            max_length: Maximum length of the summary in characters

        Returns:
            A string summary of the conversation
        """
        if not self.messages:
            return "No conversation history available."

        # Count messages by role
        user_messages = sum(1 for msg in self.messages if msg.role == "user")
        assistant_messages = sum(1 for msg in self.messages if msg.role == "assistant")

        # Get first and last messages for context
        first_user_msg = next((msg for msg in self.messages if msg.role == "user"), None)
        last_user_msg = next((msg for msg in reversed(self.messages) if msg.role == "user"), None)

        # Create summary
        summary = f"Conversation with {user_messages} user messages and {assistant_messages} assistant responses. "

        if first_user_msg:
            first_topic = first_user_msg.content[:50] + ("..." if len(first_user_msg.content) > 50 else "")
            summary += f"Started with: \"{first_topic}\" "

        if last_user_msg and last_user_msg != first_user_msg:
            last_topic = last_user_msg.content[:50] + ("..." if len(last_user_msg.content) > 50 else "")
            summary += f"Most recent question: \"{last_topic}\""

        # Ensure summary doesn't exceed max_length
        if len(summary) > max_length:
            summary = summary[:max_length-3] + "..."

        return summary

    def clear(self) -> None:
        """
        Clear the conversation history.
        """
        self.messages = []

        # Clear messages from database if conversation_id exists
        if self.conversation_id:
            try:
                with get_db() as db:
                    # Delete all messages for this conversation
                    db.query(DBMessage).filter(DBMessage.conversation_id == self.conversation_id).delete()
                    db.commit()
                    logger.info(f"Cleared messages for conversation {self.conversation_id} from database")
            except Exception as e:
                logger.error(f"Error clearing messages from database: {str(e)}")

        logger.info("Cleared conversation memory")

    @classmethod
    def load_conversation(cls, conversation_id: str, user_id: str, max_messages: int = 10) -> 'ConversationMemory':
        """
        Load a conversation from the database.

        Args:
            conversation_id: The ID of the conversation to load
            user_id: The ID of the user who owns the conversation
            max_messages: Maximum number of messages to store

        Returns:
            ConversationMemory instance
        """
        instance = cls(max_messages=max_messages, user_id=user_id, conversation_id=conversation_id)
        return instance

    @classmethod
    def get_user_conversations(cls, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all conversations for a user.

        Args:
            user_id: The ID of the user

        Returns:
            List of conversation dictionaries
        """
        try:
            with get_db() as db:
                # Use a single query with a subquery to count messages
                from sqlalchemy import func
                from sqlalchemy.orm import aliased

                # Create a subquery to count messages for each conversation
                from ragchatbot.database.models import Message as DBMessage
                message_count_subq = (
                    db.query(
                        DBMessage.conversation_id,
                        func.count(DBMessage.id).label('message_count')
                    )
                    .group_by(DBMessage.conversation_id)
                    .subquery()
                )

                # Join the conversations with the message count subquery
                conversations = (
                    db.query(
                        DBConversation,
                        func.coalesce(message_count_subq.c.message_count, 0).label('message_count')
                    )
                    .outerjoin(
                        message_count_subq,
                        DBConversation.id == message_count_subq.c.conversation_id
                    )
                    .filter(DBConversation.user_id == user_id)
                    .all()
                )

                # Convert to dictionaries
                return [{
                    'id': conv[0].id,
                    'user_id': conv[0].user_id,
                    'username': conv[0].user.username if conv[0].user else "Unknown",
                    'created_at': conv[0].created_at.isoformat(),
                    'updated_at': conv[0].updated_at.isoformat(),
                    'message_count': int(conv[1])  # Use the count from the query
                } for conv in conversations]
        except Exception as e:
            logger.error(f"Error getting user conversations: {str(e)}")
            return []

    @classmethod
    def get_all_conversations(cls) -> List[Dict[str, Any]]:
        """
        Get all conversations from all users.

        This method is intended for admin use only.

        Returns:
            List of conversation dictionaries
        """
        try:
            with get_db() as db:
                # Use a single query with a subquery to count messages
                from sqlalchemy import func

                # Create a subquery to count messages for each conversation
                from ragchatbot.database.models import Message as DBMessage
                message_count_subq = (
                    db.query(
                        DBMessage.conversation_id,
                        func.count(DBMessage.id).label('message_count')
                    )
                    .group_by(DBMessage.conversation_id)
                    .subquery()
                )

                # Join the conversations with the message count subquery
                conversations = (
                    db.query(
                        DBConversation,
                        func.coalesce(message_count_subq.c.message_count, 0).label('message_count')
                    )
                    .outerjoin(
                        message_count_subq,
                        DBConversation.id == message_count_subq.c.conversation_id
                    )
                    .all()
                )

                # Convert to dictionaries
                return [{
                    'id': conv[0].id,
                    'user_id': conv[0].user_id,
                    'username': conv[0].user.username if conv[0].user else "Unknown",
                    'created_at': conv[0].created_at.isoformat(),
                    'updated_at': conv[0].updated_at.isoformat(),
                    'message_count': int(conv[1])  # Use the count from the query
                } for conv in conversations]
        except Exception as e:
            logger.error(f"Error getting all conversations: {str(e)}")
            return []

    @classmethod
    def get_most_recent_conversation(cls, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the most recent conversation for a user.

        Args:
            user_id: The ID of the user

        Returns:
            Dictionary with conversation details or None if no conversations exist
        """
        try:
            with get_db() as db:
                # Get the most recent conversation for this user
                conversation = db.query(DBConversation).filter(
                    DBConversation.user_id == user_id
                ).order_by(DBConversation.updated_at.desc()).first()

                if not conversation:
                    return None

                # Convert to dictionary
                return {
                    'id': conversation.id,
                    'created_at': conversation.created_at.isoformat(),
                    'updated_at': conversation.updated_at.isoformat(),
                    'message_count': len(conversation.messages)
                }
        except Exception as e:
            logger.error(f"Error getting most recent conversation: {str(e)}")
            return None

    @classmethod
    def create_conversation(cls, user_id: str, title: str = None) -> Dict[str, Any]:
        """
        Create a new conversation for a user.

        Args:
            user_id: The ID of the user
            title: Optional title for the conversation

        Returns:
            Dictionary with conversation details
        """
        try:
            conversation_id = str(uuid.uuid4())
            conversation_dict = None

            with get_db() as db:
                # First, verify the user exists
                from ragchatbot.database.models import User as DBUser
                user = db.query(DBUser).filter(DBUser.id == user_id).first()

                if not user:
                    logger.error(f"User {user_id} not found when creating conversation")
                    raise ValueError(f"User {user_id} not found")

                # Create new conversation without setting relationships
                conversation = DBConversation(
                    id=conversation_id,
                    user_id=user_id,
                    title=title or "New Conversation"
                )

                # Add and commit without refreshing
                db.add(conversation)
                db.commit()

                # Create a dictionary with the conversation data
                conversation_dict = {
                    "id": conversation_id,
                    "user_id": user_id,
                    "title": title or "New Conversation",
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat(),
                    "username": user.username
                }

                logger.info(f"Created new conversation {conversation_id} for user {user_id}")

            # Return the dictionary instead of the ORM object
            return conversation_dict
        except Exception as e:
            logger.error(f"Error creating conversation for user {user_id}: {str(e)}")
            raise
