"""
Notification service for managing notifications.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import desc, or_, and_

from ragchatbot.database.db import get_db
from ragchatbot.database.models import Notification, NotificationReadStatus, UserActivity, User, Document

# Set up logging
logger = logging.getLogger(__name__)

class NotificationService:
    """Service for managing notifications."""

    @staticmethod
    def create_notification(
        title: str,
        message: str,
        notification_type: str,
        created_by: Optional[str] = None,
        user_id: Optional[str] = None,
        document_id: Optional[str] = None,
        is_global: bool = False
    ) -> Notification:
        """
        Create a new notification.

        Args:
            title: Short title for the notification
            message: Detailed message
            notification_type: Type of notification (document_added, document_deleted, etc.)
            created_by: ID of the user who triggered the notification
            user_id: ID of the target user (None for global notifications)
            document_id: ID of the related document (if any)
            is_global: Whether this is a global notification for all users

        Returns:
            The created notification
        """
        try:
            with get_db() as db:
                # If this is a global notification, create it without a user_id
                if is_global:
                    global_notification = Notification(
                        title=title,
                        message=message,
                        type=notification_type,
                        created_by=created_by,
                        user_id=None,  # No specific user
                        document_id=document_id,
                        is_global=True,
                        created_at=datetime.now()
                    )

                    db.add(global_notification)
                    db.commit()
                    db.refresh(global_notification)

                    logger.info(f"Created global notification: {global_notification.id}")

                    # Also create individual copies for all users to ensure they get notified
                    # This helps with real-time updates and tracking read status per user
                    users = db.query(User).all()
                    for user in users:
                        # Skip the user who created the notification
                        if user.id == created_by:
                            continue

                        user_notification = Notification(
                            title=title,
                            message=message,
                            type=notification_type,
                            created_by=created_by,
                            user_id=user.id,
                            document_id=document_id,
                            is_global=False,  # Individual copy is not global
                            is_read=False,
                            created_at=datetime.now()
                        )

                        db.add(user_notification)

                    db.commit()
                    logger.info(f"Created individual notifications for {len(users)} users")

                    return global_notification

                # If this is for a specific user, create a single notification
                elif user_id:
                    notification = Notification(
                        title=title,
                        message=message,
                        type=notification_type,
                        created_by=created_by,
                        user_id=user_id,
                        document_id=document_id,
                        is_global=False,
                        created_at=datetime.now()
                    )

                    db.add(notification)
                    db.commit()
                    db.refresh(notification)

                    logger.info(f"Created notification for user {user_id}: {notification.id}")
                    return notification

                # If this is a document notification with no specific user, create copies for all users with access
                elif document_id:
                    # First create a template notification
                    template_notification = Notification(
                        title=title,
                        message=message,
                        type=notification_type,
                        created_by=created_by,
                        user_id=None,
                        document_id=document_id,
                        is_global=False,
                        created_at=datetime.now()
                    )

                    db.add(template_notification)
                    db.commit()
                    db.refresh(template_notification)

                    # Get all users with access to this document
                    from ragchatbot.auth import UserManager
                    user_manager = UserManager()

                    # Get all groups with access to this document
                    document_groups = user_manager.get_document_groups(document_id)

                    # Get all users in these groups
                    affected_users = set()
                    for group_id in document_groups:
                        users_in_group = user_manager.get_group_members(group_id)
                        affected_users.update(users_in_group)

                    # Create a notification for each affected user
                    for affected_user_id in affected_users:
                        # Skip the user who created the notification
                        if affected_user_id == created_by:
                            continue

                        user_notification = Notification(
                            title=title,
                            message=message,
                            type=notification_type,
                            created_by=created_by,
                            user_id=affected_user_id,
                            document_id=document_id,
                            is_global=False,
                            is_read=False,
                            created_at=datetime.now()
                        )

                        db.add(user_notification)

                    db.commit()
                    logger.info(f"Created document notifications for {len(affected_users)} affected users")

                    return template_notification

                # Default case: create a regular notification
                else:
                    notification = Notification(
                        title=title,
                        message=message,
                        type=notification_type,
                        created_by=created_by,
                        user_id=user_id,
                        document_id=document_id,
                        is_global=is_global,
                        created_at=datetime.now()
                    )

                    db.add(notification)
                    db.commit()
                    db.refresh(notification)

                    logger.info(f"Created notification: {notification.id}")
                    return notification
        except Exception as e:
            logger.error(f"Error creating notification: {str(e)}")
            raise

    @staticmethod
    def get_user_notifications(user_id: str, limit: int = 20, include_read: bool = False) -> List[Dict[str, Any]]:
        """
        Get notifications for a specific user.

        Args:
            user_id: ID of the user
            limit: Maximum number of notifications to return
            include_read: Whether to include read notifications

        Returns:
            List of notifications
        """
        try:
            with get_db() as db:
                # Base query to get notifications visible to this user
                query = db.query(Notification).filter(
                    or_(
                        Notification.user_id == user_id,
                        Notification.is_global == True
                    )
                )

                if not include_read:
                    # Add a left join to check if the notification has been read by this user
                    query = query.outerjoin(
                        NotificationReadStatus,
                        and_(
                            NotificationReadStatus.notification_id == Notification.id,
                            NotificationReadStatus.user_id == user_id
                        )
                    ).filter(
                        NotificationReadStatus.id == None  # Only get notifications that don't have a read status
                    )

                notifications = query.order_by(desc(Notification.created_at)).limit(limit).all()

                # Convert to dictionaries and add read status
                result = []
                for notification in notifications:
                    notification_dict = notification.to_dict()

                    # Check if this notification has been read by this user
                    read_status = db.query(NotificationReadStatus).filter(
                        NotificationReadStatus.notification_id == notification.id,
                        NotificationReadStatus.user_id == user_id
                    ).first()

                    notification_dict['is_read'] = read_status is not None
                    if read_status:
                        notification_dict['read_at'] = read_status.read_at.isoformat() if read_status.read_at else None

                    result.append(notification_dict)

                return result
        except Exception as e:
            logger.error(f"Error getting notifications for user {user_id}: {str(e)}")
            return []

    @staticmethod
    def get_all_notifications(limit: int = 50, include_read: bool = False) -> List[Dict[str, Any]]:
        """
        Get all notifications (admin only).

        Args:
            limit: Maximum number of notifications to return
            include_read: Whether to include read notifications

        Returns:
            List of notifications
        """
        try:
            with get_db() as db:
                # Get all notifications
                query = db.query(Notification)

                # Get all notifications with their read status counts
                notifications = query.order_by(desc(Notification.created_at)).limit(limit).all()

                # Convert to dictionaries and add read status information
                result = []
                for notification in notifications:
                    notification_dict = notification.to_dict()

                    # Count how many users have read this notification
                    read_count = db.query(NotificationReadStatus).filter(
                        NotificationReadStatus.notification_id == notification.id
                    ).count()

                    # Get total number of users
                    total_users = db.query(User).count()

                    notification_dict['read_count'] = read_count
                    notification_dict['total_users'] = total_users
                    notification_dict['is_read_by_all'] = read_count == total_users

                    # Only include based on read status if requested
                    if include_read or not notification_dict['is_read_by_all']:
                        result.append(notification_dict)

                return result
        except Exception as e:
            logger.error(f"Error getting all notifications: {str(e)}")
            return []

    @staticmethod
    def mark_as_read(notification_id: str, user_id: str, is_admin: bool = False) -> bool:
        """
        Mark a notification as read.

        Args:
            notification_id: ID of the notification
            user_id: ID of the user marking the notification as read
            is_admin: Whether the user is an admin

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_db() as db:
                # Find the notification
                notification = db.query(Notification).filter(
                    Notification.id == notification_id
                ).first()

                if not notification:
                    logger.warning(f"Notification {notification_id} not found")
                    return False

                # Check if the user has permission to mark this notification as read
                if not is_admin and not notification.is_global and notification.user_id != user_id:
                    logger.warning(f"User {user_id} doesn't have permission to mark notification {notification_id} as read")
                    return False

                # Check if the notification is already marked as read for this user
                existing_read_status = db.query(NotificationReadStatus).filter(
                    NotificationReadStatus.notification_id == notification_id,
                    NotificationReadStatus.user_id == user_id
                ).first()

                if existing_read_status:
                    logger.info(f"Notification {notification_id} already marked as read for user {user_id}")
                    return True

                # Create a new read status entry
                read_status = NotificationReadStatus(
                    notification_id=notification_id,
                    user_id=user_id,
                    read_at=datetime.now()
                )
                db.add(read_status)
                db.commit()

                logger.info(f"Marked notification {notification_id} as read for user {user_id}")
                return True
        except Exception as e:
            logger.error(f"Error marking notification {notification_id} as read: {str(e)}")
            return False

    @staticmethod
    def mark_all_as_read(user_id: str, is_admin: bool = False) -> bool:
        """
        Mark all notifications for a user as read.

        Args:
            user_id: ID of the user
            is_admin: Whether the user is an admin (not used in this implementation)

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_db() as db:
                # Get all notifications that are visible to this user and not already marked as read
                notifications_query = db.query(Notification).filter(
                    or_(
                        Notification.user_id == user_id,
                        Notification.is_global == True
                    )
                ).outerjoin(
                    NotificationReadStatus,
                    and_(
                        NotificationReadStatus.notification_id == Notification.id,
                        NotificationReadStatus.user_id == user_id
                    )
                ).filter(
                    NotificationReadStatus.id == None  # Only get notifications that don't have a read status
                )

                notifications = notifications_query.all()

                # Create read status entries for all notifications
                for notification in notifications:
                    read_status = NotificationReadStatus(
                        notification_id=notification.id,
                        user_id=user_id,
                        read_at=datetime.now()
                    )
                    db.add(read_status)

                db.commit()
                logger.info(f"Marked {len(notifications)} notifications as read for user {user_id}")
                return True
        except Exception as e:
            logger.error(f"Error marking all notifications as read for user {user_id}: {str(e)}")
            return False

    @staticmethod
    def delete_notification(notification_id: str, user_id: str, is_admin: bool = False) -> bool:
        """
        Delete a notification.

        Args:
            notification_id: ID of the notification
            user_id: ID of the user deleting the notification
            is_admin: Whether the user is an admin

        Returns:
            True if successful, False otherwise
        """
        try:
            with get_db() as db:
                query = db.query(Notification).filter(Notification.id == notification_id)

                if not is_admin:
                    # Regular users can only delete their own notifications
                    query = query.filter(Notification.user_id == user_id)

                notification = query.first()

                if notification:
                    db.delete(notification)
                    db.commit()
                    logger.info(f"Deleted notification {notification_id}")
                    return True
                else:
                    logger.warning(f"Notification {notification_id} not found or user {user_id} doesn't have permission")
                    return False
        except Exception as e:
            logger.error(f"Error deleting notification {notification_id}: {str(e)}")
            return False

    @staticmethod
    def get_unread_count(user_id: str) -> int:
        """
        Get the number of unread notifications for a user.

        Args:
            user_id: ID of the user

        Returns:
            Number of unread notifications
        """
        try:
            with get_db() as db:
                # Get all notifications visible to this user
                count = db.query(Notification).filter(
                    or_(
                        Notification.user_id == user_id,
                        Notification.is_global == True
                    )
                ).outerjoin(
                    NotificationReadStatus,
                    and_(
                        NotificationReadStatus.notification_id == Notification.id,
                        NotificationReadStatus.user_id == user_id
                    )
                ).filter(
                    NotificationReadStatus.id == None  # Only count notifications that don't have a read status
                ).count()

                return count
        except Exception as e:
            logger.error(f"Error getting unread count for user {user_id}: {str(e)}")
            return 0

    @staticmethod
    def has_new_notifications(user_id: str, last_checked: Optional[datetime] = None) -> bool:
        """
        Check if there are new notifications since the last check.

        This method is optimized for frequent polling as it only checks
        if there are new notifications, not the actual notifications.

        Args:
            user_id: ID of the user
            last_checked: When notifications were last checked

        Returns:
            Boolean indicating if there are new notifications
        """
        try:
            with get_db() as db:
                # Base query to get notifications visible to this user
                query = db.query(Notification).filter(
                    or_(
                        Notification.user_id == user_id,
                        Notification.is_global == True
                    )
                ).outerjoin(
                    NotificationReadStatus,
                    and_(
                        NotificationReadStatus.notification_id == Notification.id,
                        NotificationReadStatus.user_id == user_id
                    )
                ).filter(
                    NotificationReadStatus.id == None  # Only get notifications that don't have a read status
                )

                # If last_checked is provided, only check for notifications after that time
                if last_checked:
                    query = query.filter(Notification.created_at > last_checked)

                # Just check if any notifications exist (more efficient than count)
                return db.query(query.exists()).scalar()
        except Exception as e:
            logger.error(f"Error checking for new notifications for user {user_id}: {str(e)}")
            return False


class ActivityService:
    """Service for tracking user activities for analytics."""

    @staticmethod
    def record_activity(
        user_id: str,
        action: str,
        document_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> UserActivity:
        """
        Record a user activity.

        Args:
            user_id: ID of the user
            action: Type of action (document_view, document_upload, chat_message, etc.)
            document_id: ID of the related document (if any)
            details: Additional details as a dictionary

        Returns:
            The created activity record
        """
        try:
            with get_db() as db:
                activity = UserActivity(
                    user_id=user_id,
                    action=action,
                    document_id=document_id,
                    details=json.dumps(details) if details else None
                )

                db.add(activity)
                db.commit()
                db.refresh(activity)

                logger.info(f"Recorded activity: {activity.id}")
                return activity
        except Exception as e:
            logger.error(f"Error recording activity for user {user_id}: {str(e)}")
            raise

    @staticmethod
    def get_user_activities(user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get activities for a specific user.

        Args:
            user_id: ID of the user
            limit: Maximum number of activities to return

        Returns:
            List of activities
        """
        try:
            with get_db() as db:
                activities = db.query(UserActivity).filter(
                    UserActivity.user_id == user_id
                ).order_by(desc(UserActivity.created_at)).limit(limit).all()

                return [activity.to_dict() for activity in activities]
        except Exception as e:
            logger.error(f"Error getting activities for user {user_id}: {str(e)}")
            return []

    @staticmethod
    def get_all_activities(limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get all activities (admin only).

        Args:
            limit: Maximum number of activities to return

        Returns:
            List of activities
        """
        try:
            with get_db() as db:
                activities = db.query(UserActivity).order_by(
                    desc(UserActivity.created_at)
                ).limit(limit).all()

                return [activity.to_dict() for activity in activities]
        except Exception as e:
            logger.error(f"Error getting all activities: {str(e)}")
            return []

    @staticmethod
    def get_document_activities(document_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get activities for a specific document.

        Args:
            document_id: ID of the document
            limit: Maximum number of activities to return

        Returns:
            List of activities
        """
        try:
            with get_db() as db:
                activities = db.query(UserActivity).filter(
                    UserActivity.document_id == document_id
                ).order_by(desc(UserActivity.created_at)).limit(limit).all()

                return [activity.to_dict() for activity in activities]
        except Exception as e:
            logger.error(f"Error getting activities for document {document_id}: {str(e)}")
            return []

    @staticmethod
    def get_activity_stats() -> Dict[str, Any]:
        """
        Get activity statistics for the dashboard.

        Returns:
            Dictionary with activity statistics
        """
        try:
            with get_db() as db:
                # Get total counts
                total_users = db.query(User).count()
                total_documents = db.query(Document).count()
                total_activities = db.query(UserActivity).count()

                # Get top users by activity count
                top_users_query = db.query(
                    UserActivity.user_id,
                    User.username,
                    db.func.count(UserActivity.id).label('activity_count')
                ).join(User, UserActivity.user_id == User.id
                ).group_by(UserActivity.user_id, User.username
                ).order_by(desc('activity_count')
                ).limit(5)

                top_users = [
                    {
                        "user_id": user_id,
                        "username": username,
                        "activity_count": activity_count
                    }
                    for user_id, username, activity_count in top_users_query
                ]

                # Get top documents by activity count
                top_documents_query = db.query(
                    UserActivity.document_id,
                    Document.filename,
                    db.func.count(UserActivity.id).label('activity_count')
                ).join(Document, UserActivity.document_id == Document.id
                ).filter(UserActivity.document_id != None
                ).group_by(UserActivity.document_id, Document.filename
                ).order_by(desc('activity_count')
                ).limit(5)

                top_documents = [
                    {
                        "document_id": document_id,
                        "document_name": filename,
                        "activity_count": activity_count
                    }
                    for document_id, filename, activity_count in top_documents_query
                ]

                # Get activity counts by type
                activity_types_query = db.query(
                    UserActivity.action,
                    db.func.count(UserActivity.id).label('count')
                ).group_by(UserActivity.action
                ).order_by(desc('count'))

                activity_types = {
                    action: count
                    for action, count in activity_types_query
                }

                return {
                    "total_users": total_users,
                    "total_documents": total_documents,
                    "total_activities": total_activities,
                    "top_users": top_users,
                    "top_documents": top_documents,
                    "activity_types": activity_types
                }
        except Exception as e:
            logger.error(f"Error getting activity stats: {str(e)}")
            return {
                "total_users": 0,
                "total_documents": 0,
                "total_activities": 0,
                "top_users": [],
                "top_documents": [],
                "activity_types": {}
            }
