"""
Document number generation utility.

This module provides functions for generating unique document numbers.
The document number format is: DOCYYYYMMXXX
Where:
- DOC is a fixed prefix
- YYYY is the 4-digit year
- MM is the 2-digit month
- XXX is a sequential number (starting from 001) that resets each month
"""

import datetime
import os
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc

from ragchatbot.database.models import Document
from ragchatbot.database.db import get_db


def get_document_type_code(filename: str) -> str:
    """
    Get a 2-3 letter code for the document type based on file extension.

    Args:
        filename: The name of the file

    Returns:
        A 2-3 letter code for the document type
    """
    # Get the file extension
    _, ext = os.path.splitext(filename)
    ext = ext.lower().strip('.')

    # Map file extensions to type codes
    type_map = {
        'pdf': 'PDF',
        'doc': 'DOC',
        'docx': 'DOC',
        'xls': 'XLS',
        'xlsx': 'XLS',
        'csv': 'CSV',
        'txt': 'TXT',
        'ppt': 'PPT',
        'pptx': 'PPT',
        'jpg': 'IMG',
        'jpeg': 'IMG',
        'png': 'IMG',
        'gif': 'IMG'
    }

    # Return the type code or a default
    return type_map.get(ext, 'OTH')


def get_current_month_prefix() -> str:
    """
    Get the current year and month as a prefix (YYYYMM).

    Returns:
        A string in the format YYYYMM
    """
    now = datetime.datetime.now()
    return f"{now.year}{now.month:02d}"


def get_next_sequence_number(db: Session, month_prefix: str, doc_type: str = None) -> int:
    """
    Get the next sequence number for the given month.

    Args:
        db: Database session
        month_prefix: The month prefix (YYYYMM)
        doc_type: The document type code (not used in the simplified format, kept for backward compatibility)

    Returns:
        The next sequence number
    """
    # Find the highest sequence number for this month
    # Document numbers are in the format DOCYYYYMMXXX
    pattern = f"DOC{month_prefix}%"

    # Query for the highest sequence number
    result = db.query(Document).filter(
        Document.document_number.like(pattern)
    ).order_by(desc(Document.document_number)).first()

    if result and result.document_number:
        # Extract the sequence number from the document number (last 3 digits)
        try:
            seq_str = result.document_number[-3:]
            return int(seq_str) + 1
        except (IndexError, ValueError):
            # If there's an error parsing the sequence, start from 1
            return 1

    # If no documents found for this month, start from 1
    return 1


def generate_document_number(db: Session, filename: str, prefix: str = "DOC") -> str:
    """
    Generate a unique document number with the specified prefix.

    Args:
        db: Database session
        filename: The name of the file
        prefix: The prefix to use for the document number (default: "DOC")

    Returns:
        A unique document number string (e.g., DOC202311001)
    """
    # Get the document type code (not used in the simplified format)
    doc_type = get_document_type_code(filename)

    # Get the current month prefix
    month_prefix = get_current_month_prefix()

    # Get the next sequence number
    seq_num = get_next_sequence_number(db, month_prefix, doc_type)

    # Format the document number with simplified format (no hyphens, no document type)
    document_number = f"{prefix}{month_prefix}{seq_num:03d}"

    return document_number


def get_unique_document_number(db: Session, filename: str, prefix: str = "DOC") -> str:
    """
    Generate a unique document number that doesn't exist in the database.

    Args:
        db: Database session
        filename: The name of the file
        prefix: The prefix to use for the document number (default: "DOC")

    Returns:
        A unique document number string (e.g., DOC202311001)
    """
    # Generate a document number
    document_number = generate_document_number(db, filename, prefix)

    # Check if the document number already exists
    existing_doc = db.query(Document).filter(Document.document_number == document_number).first()

    # If the document number already exists, increment the sequence number until we find a unique one
    if existing_doc:
        # Extract the sequence number (last 3 digits)
        seq_num = int(document_number[-3:])
        month_prefix = document_number[len(prefix):-3]

        while existing_doc:
            seq_num += 1
            document_number = f"{prefix}{month_prefix}{seq_num:03d}"
            existing_doc = db.query(Document).filter(Document.document_number == document_number).first()

    return document_number


def assign_document_number(document_id: str, prefix: str = "DOC") -> Optional[str]:
    """
    Assign a unique document number to an existing document.

    Args:
        document_id: The ID of the document to update
        prefix: The prefix to use for the document number (default: "DOC")

    Returns:
        The assigned document number if successful, None otherwise
    """
    try:
        with get_db() as db:
            # Get the document
            document = db.query(Document).filter(Document.id == document_id).first()

            if not document:
                return None

            # If the document already has a number, return it
            if document.document_number:
                return document.document_number

            # Generate a unique document number using the filename
            document_number = get_unique_document_number(db, document.filename, prefix)

            # Assign the document number
            document.document_number = document_number
            db.commit()

            return document_number
    except Exception as e:
        print(f"Error assigning document number: {str(e)}")
        return None
