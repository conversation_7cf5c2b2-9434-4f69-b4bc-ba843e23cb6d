#!/bin/bash
set -e

# Set environment variables
export DATABASE_URL="sqlite:///./chatbot.db"
export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="SyLjTkqrZoO7wnZKMCheLxzZl+qC1k9eUiXJDOYl"
export AWS_REGION="us-east-1"
export S3_BUCKET_NAME="jmscpos-sandbox-chatbot-poc-bucket"
export S3_PREFIX="chatbot_support"
export VECTOR_STORE_TYPE="db"
export JWT_SECRET_KEY="your-secret-key-change-in-production"

# Initialize the database
echo "Initializing database..."
python3 -c "
from ragchatbot.database.db import init_db
init_db()
"
echo "Database initialized!"

# Start the application
echo "Starting application..."
python3 -m uvicorn ragchatbot.api.app:app --host 0.0.0.0 --port 8000 --reload
