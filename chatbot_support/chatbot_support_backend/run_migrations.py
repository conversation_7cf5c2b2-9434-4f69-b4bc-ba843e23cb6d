#!/usr/bin/env python3
"""
Script to run database migrations.
"""

import logging
import os
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def main():
    """
    Run database migrations.
    """
    try:
        # Import the run_migrations function
        from ragchatbot.database.migrations.run_migrations import run_migrations
        
        # Run migrations
        run_migrations()
        
        logger.info("Database migrations completed successfully")
    except Exception as e:
        logger.error(f"Error running database migrations: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
