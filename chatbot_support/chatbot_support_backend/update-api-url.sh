#!/bin/bash

# This script updates the API URL in the frontend configuration
# Usage: ./update-api-url.sh <api_url>
# Example: ./update-api-url.sh https://api.example.com

if [ $# -ne 1 ]; then
    echo "Usage: $0 <api_url>"
    echo "Example: $0 https://api.example.com"
    exit 1
fi

API_URL=$1

# Create .env file for frontend
echo "Creating .env file for frontend with API URL: $API_URL"
cat > .env << EOF
REACT_APP_API_URL=$API_URL
REACT_APP_ENV=production
REACT_APP_VERSION=1.0.0
BACKEND_API_URL=http://api:8000
EOF

# Copy .env file to frontend directory
cp .env frontend/.env

echo "API URL updated successfully to: $API_URL"
echo "You can now run 'docker-compose up -d' to apply the changes"
