"""
Main RAG chatbot implementation that integrates all components.
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union

# Define a simple Document class for initialization
class SimpleDocument:
    def __init__(self, page_content, metadata=None):
        self.page_content = page_content
        self.metadata = metadata or {}

from app.data_ingestion.pdf_processor import PDFProcessor
from app.embedding.embedder import DocumentEmbedder
from app.embedding.vector_store import VectorStore
from app.retrieval.retriever import DocumentRetriever
from app.generation.generator import ResponseGenerator
from app.memory.conversation_memory import ConversationMemory

logger = logging.getLogger(__name__)

class RAGChatbot:
    """
    Main chatbot class that integrates all components for a complete RAG system.
    """

    def __init__(
        self,
        use_openai_embeddings: bool = False,
        use_google_embeddings: bool = False,
        use_google_llm: bool = False,
        openai_api_key: Optional[str] = None,
        google_api_key: Optional[str] = None,
        google_service_account_file: Optional[str] = None,
        google_project_id: Optional[str] = None,
        embedding_model: str = "all-MiniLM-L6-v2",
        llm_model_name: str = "gpt-3.5-turbo",
        vector_store_type: str = "chroma",
        vector_store_dir: str = "vector_store",
        use_compression: bool = False,
        top_k: int = 4,
        max_memory_messages: int = 20,  # Increased from 10 to 20 for better context retention
        user_id: Optional[str] = None
    ):
        """
        Initialize the RAG chatbot.

        Args:
            use_openai_embeddings: Whether to use OpenAI embeddings
            use_google_embeddings: Whether to use Google AI embeddings
            use_google_llm: Whether to use Google AI LLM
            openai_api_key: OpenAI API key
            google_api_key: Google API key
            google_service_account_file: Path to Google service account JSON file
            google_project_id: Google Cloud project ID
            embedding_model: The embedding model to use
            llm_model_name: The LLM model to use
            vector_store_type: Type of vector store ('chroma' or 'faiss')
            vector_store_dir: Directory to store the vector database
            use_compression: Whether to use contextual compression
            top_k: Number of documents to retrieve
            max_memory_messages: Maximum number of messages to store in memory
            user_id: The ID of the user who owns this chatbot instance
        """
        self.openai_api_key = openai_api_key or os.environ.get("OPENAI_API_KEY")
        self.google_api_key = google_api_key or os.environ.get("GOOGLE_API_KEY")
        self.google_service_account_file = google_service_account_file
        self.google_project_id = google_project_id
        self.user_id = user_id

        # Initialize components
        self.pdf_processor = PDFProcessor(chunk_size=1000, chunk_overlap=200)

        # Check for conflicting embedding settings
        if use_openai_embeddings and use_google_embeddings:
            raise ValueError("Cannot use both OpenAI and Google AI embeddings at the same time")

        self.embedder = DocumentEmbedder(
            embedding_model=embedding_model,
            use_openai=use_openai_embeddings,
            use_google=use_google_embeddings,
            openai_api_key=self.openai_api_key,
            google_api_key=self.google_api_key,
            google_service_account_file=self.google_service_account_file,
            google_project_id=self.google_project_id
        )

        self.vector_store = VectorStore(
            embeddings=self.embedder.embeddings,
            store_type=vector_store_type,
            persist_directory=vector_store_dir
        )

        self.retriever = None  # Will be initialized after loading documents

        self.generator = ResponseGenerator(
            model_name=llm_model_name,
            temperature=0.1,
            openai_api_key=self.openai_api_key,
            google_api_key=self.google_api_key,
            google_service_account_file=self.google_service_account_file,
            google_project_id=self.google_project_id,
            use_google=use_google_llm
        )

        # Initialize conversation memory with user_id
        self.memory = ConversationMemory(max_messages=max_memory_messages, user_id=user_id)

        # If user_id is provided, try to load the most recent conversation
        if user_id:
            try:
                # Get the most recent conversation for this user
                most_recent = ConversationMemory.get_most_recent_conversation(user_id)
                if most_recent:
                    # Set the conversation ID in memory
                    self.memory.conversation_id = most_recent.get('id')
                    # Load messages from the database
                    self.memory._load_from_database()
                    logger.info(f"Loaded most recent conversation {most_recent.get('id')} with {most_recent.get('message_count')} messages for user {user_id}")
            except Exception as e:
                logger.error(f"Error loading most recent conversation for user {user_id}: {str(e)}")

        # Configuration
        self.use_compression = use_compression
        self.top_k = top_k

        logger.info(f"Initialized RAG chatbot for user {user_id if user_id else 'unknown'}")

    def ingest_documents(self, directory_path: str, recursive: bool = True, use_s3: bool = True) -> None:
        """
        Ingest documents from a directory.

        Args:
            directory_path: Path to the directory containing PDF files
            recursive: Whether to recursively process subdirectories
            use_s3: Whether to check for S3 keys in the database
        """
        logger.info(f"Ingesting documents from {directory_path}")

        # Process PDFs - use S3 keys if available
        documents = self.pdf_processor.process_directory(directory_path, recursive=recursive, use_s3=use_s3)

        if not documents:
            logger.warning(f"No documents found in {directory_path}")
            return

        # Create vector store
        self.vector_store.create_from_documents(documents)

        # Initialize retriever with user_id for access control
        self.retriever = DocumentRetriever(
            vector_store=self.vector_store,
            use_compression=self.use_compression,
            top_k=self.top_k,
            user_id=self.user_id  # Pass user_id for access control
        )

        logger.info(f"Ingested {len(documents)} document chunks")

        # Save the vector store to ensure persistence
        self.save_vector_store()

    def add_documents(self, directory_path: str, recursive: bool = True, use_s3: bool = True) -> None:
        """
        Add documents to an existing vector store.

        Args:
            directory_path: Path to the directory containing PDF files
            recursive: Whether to recursively process subdirectories
            use_s3: Whether to check for S3 keys in the database
        """
        if not self.vector_store.vector_store:
            logger.warning("Vector store not initialized. Call ingest_documents first.")
            return

        logger.info(f"Adding documents from {directory_path}")

        # Process PDFs - use S3 keys if available
        documents = self.pdf_processor.process_directory(directory_path, recursive=recursive, use_s3=use_s3)

        if not documents:
            logger.warning(f"No documents found in {directory_path}")
            return

        # Add to vector store
        self.vector_store.add_documents(documents)

        logger.info(f"Added {len(documents)} document chunks")

        # Save the vector store to ensure persistence
        self.save_vector_store()

    def chat(self, user_message: str) -> Dict[str, Any]:
        """
        Process a user message and generate a response.

        Args:
            user_message: The user's message

        Returns:
            Dictionary containing the chatbot's response text, document references, and any images
        """
        # Add user message to memory
        self.memory.add_user_message(user_message)

        # Check if retriever is initialized
        if not self.retriever:
            logger.warning("Retriever not initialized. Attempting to initialize it now.")

            # Try to initialize the retriever if vector store exists
            if self.vector_store and self.vector_store.vector_store is not None:
                try:
                    # For database vector store, check if there are any embeddings
                    if self.vector_store.store_type == "db":
                        from app.database.db import get_db
                        from app.database.models import Embedding as DBEmbedding

                        with get_db() as db:
                            embedding_count = db.query(DBEmbedding).count()
                            if embedding_count > 0:
                                logger.info(f"Found {embedding_count} embeddings in database, initializing retriever")
                                self.retriever = DocumentRetriever(
                                    vector_store=self.vector_store,
                                    use_compression=self.use_compression,
                                    top_k=self.top_k,
                                    user_id=self.user_id  # Pass user_id for access control
                                )
                    else:
                        # For file-based vector stores, initialize the retriever
                        self.retriever = DocumentRetriever(
                            vector_store=self.vector_store,
                            use_compression=self.use_compression,
                            top_k=self.top_k,
                            user_id=self.user_id  # Pass user_id for access control
                        )
                except Exception as e:
                    logger.error(f"Error initializing retriever: {str(e)}")

        # If retriever is still not initialized, return a default response
        if not self.retriever:
            logger.warning("Retriever not initialized. Call ingest_documents first.")
            response_text = "I haven't been trained on any documents yet. Please ingest some documents first."
            self.memory.add_assistant_message(response_text)
            return {"text": response_text, "images": [], "document_references": []}

        # Retrieve relevant documents
        try:
            documents = self.retriever.retrieve(user_message)

            # Get conversation history for context
            # Use the appropriate format based on whether we're using Google or OpenAI
            format_type = "gemini" if self.generator.use_google else "openai"

            # Get conversation summary for additional context
            conversation_summary = self.memory.get_conversation_summary(max_length=300)

            # Get formatted conversation history
            conversation_history = self.memory.get_formatted_history(
                include_timestamps=False,
                format_type=format_type
            )

            # Add summary to the beginning of the conversation history for better context
            if conversation_history and conversation_summary:
                conversation_history = f"CONVERSATION SUMMARY: {conversation_summary}\n\n{conversation_history}"

            logger.info(f"Using conversation history with {len(self.memory.messages)} messages in {format_type} format")

            # Generate response with conversation history
            response = self.generator.generate_response(
                query=user_message,
                documents=documents,
                conversation_history=conversation_history
            )

            # Check if response is a dictionary (new format) or string (old format)
            if isinstance(response, dict):
                response_text = response.get("text", "")
                images = response.get("images", [])
                document_references = response.get("document_references", [])
            else:
                # Handle legacy format (string response)
                response_text = response
                images = []
                document_references = []

            # Add assistant message to memory (just the text part)
            self.memory.add_assistant_message(response_text)

            # Return the full response with images and document references
            return {
                "text": response_text,
                "images": images,
                "document_references": document_references
            }
        except Exception as e:
            logger.error(f"Error retrieving documents or generating response: {str(e)}")
            response_text = "I'm having trouble accessing the document database. Please try again later or contact support."
            self.memory.add_assistant_message(response_text)
            return {"text": response_text, "images": [], "document_references": []}

    def get_conversation_history(self, format_type: str = "default") -> str:
        """
        Get the formatted conversation history.

        Args:
            format_type: The format type to use ("default", "gemini", "openai")

        Returns:
            Formatted conversation history
        """
        return self.memory.get_formatted_history(format_type=format_type)

    def clear_conversation(self) -> None:
        """
        Clear the conversation history.
        """
        self.memory.clear()

    def delete_document(self, document_id: str) -> bool:
        """
        Delete a document from the chatbot's vector store.

        Args:
            document_id: The ID of the document to delete

        Returns:
            True if the document was deleted, False otherwise
        """
        if not self.vector_store:
            logger.warning("Vector store not initialized. Nothing to delete.")
            return False

        # Delete the document from the vector store
        success = self.vector_store.delete_document(document_id)

        if success:
            # Reinitialize the retriever to ensure it uses the updated vector store
            retriever_success = self.initialize_retriever()
            if retriever_success:
                logger.info(f"Reinitialized retriever after deleting document {document_id}")
            else:
                logger.warning(f"Failed to reinitialize retriever after deleting document {document_id}")

            # Save the vector store to persist changes
            self.save_vector_store()

            # Clear conversation memory to remove any references to the deleted document
            self.clear_conversation()
            logger.info(f"Cleared conversation memory after deleting document {document_id}")

            return True
        else:
            logger.warning(f"Failed to delete document {document_id} from vector store")
            return False

    def initialize_retriever(self) -> bool:
        """
        Initialize or reinitialize the retriever.

        This method checks if the vector store is initialized and creates a new retriever if needed.

        Returns:
            True if the retriever was initialized successfully, False otherwise
        """
        try:
            if self.vector_store and self.vector_store.vector_store is not None:
                self.retriever = DocumentRetriever(
                    vector_store=self.vector_store,
                    use_compression=self.use_compression,
                    top_k=self.top_k,
                    user_id=self.user_id  # Pass user_id for access control
                )
                logger.info(f"Initialized retriever successfully for user {self.user_id}")
                return True
            else:
                logger.warning("Vector store not initialized. Cannot initialize retriever.")
                return False
        except Exception as e:
            logger.error(f"Error initializing retriever: {str(e)}")
            return False

    def reset_vector_store(self) -> bool:
        """
        Reset the vector store to fix inconsistencies.
        This will completely clear the vector store and reinitialize it.

        Returns:
            True if the reset was successful, False otherwise
        """
        if not self.vector_store:
            logger.warning("Vector store not initialized. Nothing to reset.")
            return False

        # Reset the vector store
        logger.info("Resetting vector store to fix inconsistencies")
        success = self.vector_store.reset()

        if success:
            # Reinitialize the retriever with the empty vector store
            success = self.initialize_retriever()
            if success:
                logger.info("Reinitialized retriever with empty vector store")
            else:
                logger.warning("Failed to reinitialize retriever after vector store reset")

            # Clear conversation memory
            self.clear_conversation()
            logger.info("Cleared conversation memory after vector store reset")

            return True
        else:
            logger.warning("Failed to reset vector store")
            return False

    def save_vector_store(self, save_path: Optional[str] = None) -> None:
        """
        Save the vector store to disk.

        Args:
            save_path: Path to save the vector store
        """
        if not self.vector_store.vector_store:
            logger.warning("Vector store not initialized. Call ingest_documents first.")
            return

        # Use the vector store's persist_directory if available and no save_path is provided
        if not save_path and hasattr(self.vector_store, 'persist_directory') and self.vector_store.persist_directory:
            save_path = self.vector_store.persist_directory
            logger.info(f"Using vector store's persist_directory: {save_path}")
        else:
            save_path = save_path or "vector_store"

        # Ensure the directory exists
        os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else save_path, exist_ok=True)

        # Save the vector store
        logger.info(f"Saving vector store to {save_path}")
        self.vector_store.save(save_path)

        # For Chroma, explicitly call persist if available
        if self.vector_store.store_type == "chroma" and hasattr(self.vector_store.vector_store, 'persist'):
            logger.info(f"Explicitly calling persist() on Chroma vector store")
            self.vector_store.vector_store.persist()

        logger.info(f"Vector store saved successfully to {save_path}")

    @classmethod
    def load(
        cls,
        vector_store_path: str,
        use_openai_embeddings: bool = False,
        use_google_embeddings: bool = False,
        use_google_llm: bool = False,
        openai_api_key: Optional[str] = None,
        google_api_key: Optional[str] = None,
        google_service_account_file: Optional[str] = None,
        google_project_id: Optional[str] = None,
        embedding_model: str = "all-MiniLM-L6-v2",
        llm_model_name: str = "gpt-3.5-turbo",
        vector_store_type: str = "chroma",
        use_compression: bool = False,
        top_k: int = 4,
        max_memory_messages: int = 10,
        user_id: Optional[str] = None
    ) -> 'RAGChatbot':
        """
        Load a chatbot with a pre-existing vector store.

        Args:
            vector_store_path: Path to the vector store
            use_openai_embeddings: Whether to use OpenAI embeddings
            use_google_embeddings: Whether to use Google AI embeddings
            use_google_llm: Whether to use Google AI LLM
            openai_api_key: OpenAI API key
            google_api_key: Google API key
            google_service_account_file: Path to Google service account JSON file
            google_project_id: Google Cloud project ID
            embedding_model: The embedding model to use
            llm_model_name: The LLM model to use
            vector_store_type: Type of vector store ('chroma' or 'faiss')
            use_compression: Whether to use contextual compression
            top_k: Number of documents to retrieve
            max_memory_messages: Maximum number of messages to store in memory
            user_id: The ID of the user who owns this chatbot instance

        Returns:
            Initialized RAGChatbot instance
        """
        instance = cls(
            use_openai_embeddings=use_openai_embeddings,
            use_google_embeddings=use_google_embeddings,
            use_google_llm=use_google_llm,
            openai_api_key=openai_api_key,
            google_api_key=google_api_key,
            google_service_account_file=google_service_account_file,
            google_project_id=google_project_id,
            embedding_model=embedding_model,
            llm_model_name=llm_model_name,
            vector_store_type=vector_store_type,
            vector_store_dir=vector_store_path,
            use_compression=use_compression,
            top_k=top_k,
            max_memory_messages=max_memory_messages,
            user_id=user_id
        )

        # Load the vector store
        instance.vector_store = VectorStore.load(
            load_path=vector_store_path,
            embeddings=instance.embedder.embeddings,
            store_type=vector_store_type
        )

        # Make sure the vector store is properly initialized
        if not instance.vector_store.vector_store:
            logger.warning("Vector store not properly loaded. Initializing a new one.")
            instance.vector_store = VectorStore(
                embeddings=instance.embedder.embeddings,
                store_type=vector_store_type,
                persist_directory=vector_store_path
            )

            # Create an empty vector store if it doesn't exist
            if not instance.vector_store.vector_store:
                logger.info("Creating empty vector store")
                # Skip creating an empty vector store - we'll initialize it when we add documents
                logger.info("Skipping empty vector store creation - will initialize when documents are added")
                # Set a flag to indicate that the vector store needs to be initialized
                instance.vector_store_initialized = False

        # Initialize retriever
        try:
            # Check if the vector store has any documents
            if instance.vector_store.vector_store is not None:
                # For database vector store, we need to check if there are any embeddings
                if instance.vector_store.store_type == "db":
                    from app.database.db import get_db
                    from app.database.models import Embedding as DBEmbedding

                    with get_db() as db:
                        embedding_count = db.query(DBEmbedding).count()
                        if embedding_count > 0:
                            logger.info(f"Found {embedding_count} embeddings in database")
                            instance.retriever = DocumentRetriever(
                                vector_store=instance.vector_store,
                                use_compression=use_compression,
                                top_k=top_k
                            )
                        else:
                            logger.warning("No embeddings found in database. Retriever will be initialized when documents are added.")
                            instance.retriever = None
                else:
                    # For file-based vector stores, initialize the retriever
                    instance.retriever = DocumentRetriever(
                        vector_store=instance.vector_store,
                        use_compression=use_compression,
                        top_k=top_k
                    )
            else:
                logger.warning("Vector store not initialized. Retriever will be initialized when documents are added.")
                instance.retriever = None
        except Exception as e:
            logger.error(f"Error initializing retriever: {str(e)}")
            instance.retriever = None

        # If user_id is provided, try to load the most recent conversation
        if user_id:
            try:
                # Get the most recent conversation for this user
                most_recent = ConversationMemory.get_most_recent_conversation(user_id)
                if most_recent:
                    # Set the conversation ID in memory
                    instance.memory.conversation_id = most_recent.get('id')
                    # Load messages from the database
                    instance.memory._load_from_database()
                    logger.info(f"Loaded most recent conversation {most_recent.get('id')} with {most_recent.get('message_count')} messages for user {user_id}")
            except Exception as e:
                logger.error(f"Error loading most recent conversation for user {user_id}: {str(e)}")

        logger.info(f"Loaded chatbot with vector store from {vector_store_path}")
        return instance
