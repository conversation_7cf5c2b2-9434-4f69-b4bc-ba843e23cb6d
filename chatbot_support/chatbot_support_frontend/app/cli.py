"""
Command-line interface for the RAG chatbot.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Optional

from app.chatbot import RAGChatbot

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def setup_argparse():
    """
    Set up command-line argument parsing.

    Returns:
        ArgumentParser instance
    """
    parser = argparse.ArgumentParser(description="RAG Chatbot CLI")

    # Subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Chat command
    chat_parser = subparsers.add_parser("chat", help="Start an interactive chat session")
    chat_parser.add_argument("--vector-store", type=str, default="vector_store", help="Path to vector store")
    chat_parser.add_argument("--model", type=str, default="gpt-3.5-turbo", help="LLM model to use")
    chat_parser.add_argument("--embedding-model", type=str, default="all-MiniLM-L6-v2", help="Embedding model to use")
    chat_parser.add_argument("--use-openai-embeddings", action="store_true", help="Use OpenAI embeddings")
    chat_parser.add_argument("--use-google-embeddings", action="store_true", help="Use Google AI embeddings")
    chat_parser.add_argument("--use-google-llm", action="store_true", help="Use Google AI LLM (Gemini)")
    chat_parser.add_argument("--top-k", type=int, default=4, help="Number of documents to retrieve")

    # Ingest command
    ingest_parser = subparsers.add_parser("ingest", help="Ingest documents")
    ingest_parser.add_argument("directory", type=str, help="Directory containing PDF files")
    ingest_parser.add_argument("--vector-store", type=str, default="vector_store", help="Path to vector store")
    ingest_parser.add_argument("--embedding-model", type=str, default="all-MiniLM-L6-v2", help="Embedding model to use")
    ingest_parser.add_argument("--use-openai-embeddings", action="store_true", help="Use OpenAI embeddings")
    ingest_parser.add_argument("--use-google-embeddings", action="store_true", help="Use Google AI embeddings")
    ingest_parser.add_argument("--recursive", action="store_true", help="Recursively process subdirectories")

    # Add command
    add_parser = subparsers.add_parser("add", help="Add documents to existing vector store")
    add_parser.add_argument("directory", type=str, help="Directory containing PDF files")
    add_parser.add_argument("--vector-store", type=str, default="vector_store", help="Path to vector store")
    add_parser.add_argument("--embedding-model", type=str, default="all-MiniLM-L6-v2", help="Embedding model to use")
    add_parser.add_argument("--use-openai-embeddings", action="store_true", help="Use OpenAI embeddings")
    add_parser.add_argument("--use-google-embeddings", action="store_true", help="Use Google AI embeddings")
    add_parser.add_argument("--recursive", action="store_true", help="Recursively process subdirectories")

    # Server command
    server_parser = subparsers.add_parser("server", help="Start the API server")

    return parser

def chat_loop(chatbot: RAGChatbot):
    """
    Run an interactive chat loop.

    Args:
        chatbot: Initialized RAGChatbot instance
    """
    print("\nRAG Chatbot Interactive Session")
    print("Type 'exit', 'quit', or press Ctrl+C to end the session")
    print("Type 'clear' to clear the conversation history")
    print("Type 'history' to show the conversation history")
    print("-" * 50)

    try:
        while True:
            user_input = input("\nYou: ").strip()

            if user_input.lower() in ["exit", "quit"]:
                print("Ending session. Goodbye!")
                break

            if user_input.lower() == "clear":
                chatbot.clear_conversation()
                print("Conversation history cleared.")
                continue

            if user_input.lower() == "history":
                history = chatbot.get_conversation_history()
                if history:
                    print("\nConversation History:")
                    print(history)
                else:
                    print("No conversation history yet.")
                continue

            if not user_input:
                continue

            response = chatbot.chat(user_input)
            print(f"\nChatbot: {response}")

    except KeyboardInterrupt:
        print("\nEnding session. Goodbye!")
    except Exception as e:
        logger.error(f"Error in chat loop: {str(e)}")
        print(f"\nAn error occurred: {str(e)}")

def main():
    """
    Main entry point for the CLI.
    """
    parser = setup_argparse()
    args = parser.parse_args()

    # Check for API keys if needed
    if getattr(args, "use_openai_embeddings", False) or getattr(args, "model", "").startswith("gpt"):
        openai_api_key = os.environ.get("OPENAI_API_KEY")
        if not openai_api_key:
            print("Error: OpenAI API key not found. Please set the OPENAI_API_KEY environment variable.")
            sys.exit(1)

    if getattr(args, "use_google_embeddings", False) or getattr(args, "use_google_llm", False):
        google_api_key = os.environ.get("GOOGLE_API_KEY")
        if not google_api_key:
            print("Error: Google API key not found. Please set the GOOGLE_API_KEY environment variable.")
            sys.exit(1)

    # Handle commands
    if args.command == "chat":
        # Check if vector store exists
        vector_store_path = Path(args.vector_store)
        if not vector_store_path.exists():
            print(f"Error: Vector store not found at {args.vector_store}")
            print("Please ingest documents first using the 'ingest' command.")
            sys.exit(1)

        # Load chatbot
        try:
            chatbot = app.load(
                vector_store_path=args.vector_store,
                use_openai_embeddings=args.use_openai_embeddings,
                use_google_embeddings=args.use_google_embeddings,
                use_google_llm=args.use_google_llm,
                embedding_model=args.embedding_model,
                llm_model_name=args.model,
                top_k=args.top_k
            )

            # Start chat loop
            chat_loop(chatbot)

        except Exception as e:
            logger.error(f"Error loading chatbot: {str(e)}")
            print(f"Error loading chatbot: {str(e)}")
            sys.exit(1)

    elif args.command == "ingest":
        # Check if directory exists
        directory_path = Path(args.directory)
        if not directory_path.exists() or not directory_path.is_dir():
            print(f"Error: Directory not found: {args.directory}")
            sys.exit(1)

        # Initialize chatbot
        try:
            chatbot = RAGChatbot(
                use_openai_embeddings=args.use_openai_embeddings,
                use_google_embeddings=args.use_google_embeddings,
                embedding_model=args.embedding_model,
                vector_store_dir=args.vector_store
            )

            # Ingest documents
            print(f"Ingesting documents from {args.directory}...")
            chatbot.ingest_documents(args.directory, recursive=args.recursive)

            # Save vector store
            chatbot.save_vector_store()
            print(f"Documents ingested and vector store saved to {args.vector_store}")

        except Exception as e:
            logger.error(f"Error ingesting documents: {str(e)}")
            print(f"Error ingesting documents: {str(e)}")
            sys.exit(1)

    elif args.command == "add":
        # Check if directory exists
        directory_path = Path(args.directory)
        if not directory_path.exists() or not directory_path.is_dir():
            print(f"Error: Directory not found: {args.directory}")
            sys.exit(1)

        # Check if vector store exists
        vector_store_path = Path(args.vector_store)
        if not vector_store_path.exists():
            print(f"Error: Vector store not found at {args.vector_store}")
            print("Please ingest documents first using the 'ingest' command.")
            sys.exit(1)

        # Load chatbot
        try:
            chatbot = app.load(
                vector_store_path=args.vector_store,
                use_openai_embeddings=args.use_openai_embeddings,
                use_google_embeddings=args.use_google_embeddings,
                embedding_model=args.embedding_model
            )

            # Add documents
            print(f"Adding documents from {args.directory}...")
            chatbot.add_documents(args.directory, recursive=args.recursive)

            # Save vector store
            chatbot.save_vector_store()
            print(f"Documents added and vector store saved to {args.vector_store}")

        except Exception as e:
            logger.error(f"Error adding documents: {str(e)}")
            print(f"Error adding documents: {str(e)}")
            sys.exit(1)

    elif args.command == "server":
        try:
            from app.api.app import start
            print("Starting API server...")
            start()
        except Exception as e:
            logger.error(f"Error starting server: {str(e)}")
            print(f"Error starting server: {str(e)}")
            sys.exit(1)

    else:
        parser.print_help()

if __name__ == "__main__":
    main()
