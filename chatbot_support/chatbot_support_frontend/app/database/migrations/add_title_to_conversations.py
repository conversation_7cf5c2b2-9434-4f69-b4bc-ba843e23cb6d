"""
Database migration script to add title column to conversations table.
"""

import logging
import os
import sys

# Add the parent directory to the path so we can import the database modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from sqlalchemy import text

from app.database.db import get_db, engine

logger = logging.getLogger(__name__)

def run_migration():
    """
    Run the migration to add title column to conversations table.
    """
    try:
        # Check if the column already exists
        with get_db() as db:
            # Try to check if the column exists in MySQL
            result = db.execute(text("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'ragchatbot' AND table_name = 'conversations' AND column_name = 'title'"))
            column_exists = result.scalar() > 0
            
            if column_exists:
                logger.info("title column already exists in conversations table")
                return

        # Add the column
        with engine.connect() as connection:
            connection.execute(text('ALTER TABLE conversations ADD COLUMN title VARCHAR(255)'))
            
        logger.info("Successfully added title column to conversations table")
        
        # Update existing conversations to set title based on first message
        with get_db() as db:
            # Get all conversations
            result = db.execute(text("""
                UPDATE conversations c
                LEFT JOIN (
                    SELECT 
                        conversation_id,
                        SUBSTRING(content, 1, 50) as first_content
                    FROM messages
                    WHERE role = 'user'
                    AND id IN (
                        SELECT MIN(id)
                        FROM messages
                        WHERE role = 'user'
                        GROUP BY conversation_id
                    )
                ) m ON c.id = m.conversation_id
                SET c.title = CASE 
                    WHEN m.first_content IS NULL THEN 'New Conversation'
                    WHEN LENGTH(m.first_content) < 50 THEN m.first_content
                    ELSE CONCAT(SUBSTRING(m.first_content, 1, 47), '...')
                END
                WHERE c.title IS NULL
            """))
            
            # Commit the changes
            db.commit()
            
        logger.info("Updated existing conversations with titles")
        
    except Exception as e:
        logger.error(f"Error running migration: {str(e)}")
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the migration
    run_migration()
