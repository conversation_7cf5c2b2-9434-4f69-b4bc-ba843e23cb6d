"""
Script to run database migrations.
"""

import importlib
import sys

def run_migration(migration_name, action="upgrade"):
    """
    Run a specific migration.
    
    Args:
        migration_name: Name of the migration module
        action: 'upgrade' or 'downgrade'
    """
    try:
        # Import the migration module
        migration = importlib.import_module(f"app.database.migrations.{migration_name}")
        
        # Run the specified action
        if action == "upgrade":
            migration.upgrade()
            print(f"Successfully upgraded {migration_name}")
        elif action == "downgrade":
            migration.downgrade()
            print(f"Successfully downgraded {migration_name}")
        else:
            print(f"Unknown action: {action}")
            sys.exit(1)
    except Exception as e:
        print(f"Error running migration {migration_name}: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python -m app.database.migrations.run_migration <migration_name> [upgrade|downgrade]")
        sys.exit(1)
    
    migration_name = sys.argv[1]
    action = sys.argv[2] if len(sys.argv) > 2 else "upgrade"
    
    run_migration(migration_name, action)
