"""
Database models for the RAG chatbot.
"""

import uuid
import json
import base64
import numpy as np
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from sqlalchemy import Column, String, Text, Boolean, Integer, BigInteger, ForeignKey, DateTime, Table, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from .db import Base

class User(Base):
    """User model."""
    __tablename__ = "users"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False, default="user")
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)

    # Relationships
    groups = relationship("Group", secondary="user_groups", back_populates="members")
    user_groups = relationship("UserGroup", back_populates="user")
    documents = relationship("Document", back_populates="uploader")
    conversations = relationship("Conversation", back_populates="user")

    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary."""
        return {
            "id": self.id,
            "username": self.username,
            "role": self.role,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "groups": [group.id for group in self.groups]
        }

class Group(Base):
    """Group model."""
    __tablename__ = "groups"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), unique=True, nullable=False)
    description = Column(Text, nullable=True)

    # Relationships
    members = relationship("User", secondary="user_groups", back_populates="groups")
    group_users = relationship("UserGroup", back_populates="group")
    documents = relationship("Document", secondary="document_access", back_populates="accessible_by")
    group_documents = relationship("DocumentAccess", back_populates="group")

    @property
    def group_id(self):
        """Alias for id to maintain compatibility."""
        return self.id

    def to_dict(self) -> Dict[str, Any]:
        """Convert group to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "members": [member.id for member in self.members]
        }

# Association table for User-Group many-to-many relationship
class UserGroup(Base):
    """User-Group association model."""
    __tablename__ = "user_groups"

    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), primary_key=True)
    group_id = Column(String(36), ForeignKey("groups.id", ondelete="CASCADE"), primary_key=True)

    # Add relationships for easier access
    user = relationship("User", back_populates="user_groups")
    group = relationship("Group", back_populates="group_users")

class Document(Base):
    """Document model."""
    __tablename__ = "documents"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    document_number = Column(String(20), unique=True, nullable=True)  # Auto-generated document number (e.g., DOC12345)
    filename = Column(String(255), nullable=False)
    s3_key = Column(String(255), nullable=False)
    content_type = Column(String(100), nullable=False)
    size = Column(BigInteger, nullable=False)
    uploaded_by = Column(String(36), ForeignKey("users.id"), nullable=False)
    uploaded_at = Column(DateTime, default=datetime.utcnow)
    processed = Column(Boolean, default=False)
    processed_at = Column(DateTime, nullable=True)
    # New metadata fields
    service_name = Column(String(255), nullable=True)
    software_menus = Column(String(255), nullable=True)
    issue_type = Column(String(255), nullable=True)

    # Relationships
    uploader = relationship("User", back_populates="documents")
    embeddings = relationship("Embedding", back_populates="document", cascade="all, delete-orphan")
    accessible_by = relationship("Group", secondary="document_access", back_populates="documents")
    document_groups = relationship("DocumentAccess", back_populates="document")

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary."""
        return {
            "id": self.id,
            "document_number": self.document_number,
            "filename": self.filename,
            "s3_key": self.s3_key,
            "content_type": self.content_type,
            "size": self.size,
            "uploaded_by": self.uploaded_by,
            "uploaded_at": self.uploaded_at.isoformat() if self.uploaded_at else None,
            "processed": self.processed,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "accessible_by": [group.id for group in self.accessible_by],
            # Include the new metadata fields
            "service_name": self.service_name,
            "software_menus": self.software_menus,
            "issue_type": self.issue_type
        }

# Association table for Document-Group many-to-many relationship
class DocumentAccess(Base):
    """Document-Group association model."""
    __tablename__ = "document_access"

    document_id = Column(String(36), ForeignKey("documents.id", ondelete="CASCADE"), primary_key=True)
    group_id = Column(String(36), ForeignKey("groups.id", ondelete="CASCADE"), primary_key=True)

    # Add relationships for easier access
    document = relationship("Document", back_populates="document_groups")
    group = relationship("Group", back_populates="group_documents")

    @property
    def id(self):
        """Alias for group_id to maintain compatibility."""
        return self.group_id

class Embedding(Base):
    """Embedding model."""
    __tablename__ = "embeddings"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    document_id = Column(String(36), ForeignKey("documents.id", ondelete="CASCADE"), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    chunk_text = Column(Text, nullable=False)
    embedding_vector = Column(Text, nullable=False)  # Store as base64 encoded string
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    document = relationship("Document", back_populates="embeddings")

    @hybrid_property
    def vector(self) -> np.ndarray:
        """Get the embedding vector as a numpy array."""
        vector_bytes = base64.b64decode(self.embedding_vector)
        return np.frombuffer(vector_bytes, dtype=np.float32)

    @vector.setter
    def vector(self, vector: np.ndarray):
        """Set the embedding vector from a numpy array."""
        vector_bytes = vector.astype(np.float32).tobytes()
        self.embedding_vector = base64.b64encode(vector_bytes).decode('utf-8')

    def to_dict(self) -> Dict[str, Any]:
        """Convert embedding to dictionary."""
        return {
            "id": self.id,
            "document_id": self.document_id,
            "chunk_index": self.chunk_index,
            "chunk_text": self.chunk_text,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class Conversation(Base):
    """Conversation model."""
    __tablename__ = "conversations"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    title = Column(String(255), nullable=True)  # Optional title for the conversation
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan", order_by="Message.created_at")

    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "username": self.user.username if self.user else "Unknown",
            "title": self.title,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "messages": [message.to_dict() for message in self.messages]
        }

class Message(Base):
    """Message model."""
    __tablename__ = "messages"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    conversation_id = Column(String(36), ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=True)  # Added user_id field
    role = Column(String(50), nullable=False)  # 'user', 'assistant', or 'system'
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    user = relationship("User")  # Add relationship to User model

    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return {
            "id": self.id,
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "role": self.role,
            "content": self.content,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class ChatbotConfig(Base):
    """Chatbot configuration model."""
    __tablename__ = "chatbot_configs"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    use_openai_embeddings = Column(Boolean, default=False)
    use_google_embeddings = Column(Boolean, default=False)
    use_google_llm = Column(Boolean, default=False)
    openai_api_key = Column(String(255), nullable=True)
    google_api_key = Column(String(255), nullable=True)
    google_service_account_file = Column(String(255), nullable=True)
    google_project_id = Column(String(255), nullable=True)
    embedding_model = Column(String(255), default="all-MiniLM-L6-v2")
    llm_model_name = Column(String(255), default="gpt-3.5-turbo")
    vector_store_type = Column(String(50), default="chroma")
    vector_store_dir = Column(String(255), nullable=False)
    use_compression = Column(Boolean, default=False)
    top_k = Column(Integer, default=4)
    max_memory_messages = Column(Integer, default=10)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", backref="chatbot_configs")

    def to_dict(self) -> Dict[str, Any]:
        """Convert chatbot config to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "use_openai_embeddings": self.use_openai_embeddings,
            "use_google_embeddings": self.use_google_embeddings,
            "use_google_llm": self.use_google_llm,
            "openai_api_key": self.openai_api_key,
            "google_api_key": self.google_api_key,
            "google_service_account_file": self.google_service_account_file,
            "google_project_id": self.google_project_id,
            "embedding_model": self.embedding_model,
            "llm_model_name": self.llm_model_name,
            "vector_store_type": self.vector_store_type,
            "vector_store_dir": self.vector_store_dir,
            "use_compression": self.use_compression,
            "top_k": self.top_k,
            "max_memory_messages": self.max_memory_messages,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class Notification(Base):
    """Notification model for system events."""
    __tablename__ = "notifications"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=True)  # Target user (null for all users)
    created_by = Column(String(36), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)  # User who triggered the notification
    document_id = Column(String(36), ForeignKey("documents.id", ondelete="SET NULL"), nullable=True)  # Related document if any
    type = Column(String(50), nullable=False)  # Type of notification: 'document_added', 'document_deleted', 'user_added', etc.
    title = Column(String(255), nullable=False)  # Short title for the notification
    message = Column(Text, nullable=False)  # Detailed message
    is_read = Column(Boolean, default=False)  # Whether the notification has been read
    is_global = Column(Boolean, default=False)  # Whether this is a global notification for all users
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    creator = relationship("User", foreign_keys=[created_by])
    document = relationship("Document")

    def to_dict(self) -> Dict[str, Any]:
        """Convert notification to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "created_by": self.created_by,
            "creator_name": self.creator.username if self.creator else "System",
            "document_id": self.document_id,
            "document_name": self.document.filename if self.document else None,
            "type": self.type,
            "title": self.title,
            "message": self.message,
            "is_read": self.is_read,
            "is_global": self.is_global,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class NotificationReadStatus(Base):
    """Tracks which users have read which notifications."""
    __tablename__ = "notification_read_status"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    notification_id = Column(String(36), ForeignKey("notifications.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    read_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    notification = relationship("Notification")
    user = relationship("User")

    # Add a unique constraint to ensure a user can only mark a notification as read once
    __table_args__ = (
        UniqueConstraint('notification_id', 'user_id', name='uix_notification_user'),
    )

    def to_dict(self) -> Dict[str, Any]:
        """Convert read status to dictionary."""
        return {
            "id": self.id,
            "notification_id": self.notification_id,
            "user_id": self.user_id,
            "read_at": self.read_at.isoformat() if self.read_at else None
        }


class UserActivity(Base):
    """User activity model for analytics."""
    __tablename__ = "user_activities"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    document_id = Column(String(36), ForeignKey("documents.id", ondelete="SET NULL"), nullable=True)
    action = Column(String(50), nullable=False)  # 'document_view', 'document_upload', 'chat_message', etc.
    details = Column(Text, nullable=True)  # Additional details as JSON
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User")
    document = relationship("Document")

    def to_dict(self) -> Dict[str, Any]:
        """Convert user activity to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "username": self.user.username if self.user else "Unknown",
            "document_id": self.document_id,
            "document_name": self.document.filename if self.document else None,
            "action": self.action,
            "details": json.loads(self.details) if self.details else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class ServiceNameOption(Base):
    """Service name dropdown option model."""
    __tablename__ = "service_name_options"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    value = Column(String(255), nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(36), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)

    # Relationships
    creator = relationship("User")

    def to_dict(self) -> Dict[str, Any]:
        """Convert service name option to dictionary."""
        return {
            "id": self.id,
            "value": self.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "created_by": self.created_by
        }


class SoftwareMenuOption(Base):
    """Software menu dropdown option model."""
    __tablename__ = "software_menu_options"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    value = Column(String(255), nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(36), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)

    # Relationships
    creator = relationship("User")

    def to_dict(self) -> Dict[str, Any]:
        """Convert software menu option to dictionary."""
        return {
            "id": self.id,
            "value": self.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "created_by": self.created_by
        }


class IssueTypeOption(Base):
    """Issue type dropdown option model."""
    __tablename__ = "issue_type_options"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    value = Column(String(255), nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(36), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)

    # Relationships
    creator = relationship("User")

    def to_dict(self) -> Dict[str, Any]:
        """Convert issue type option to dictionary."""
        return {
            "id": self.id,
            "value": self.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "created_by": self.created_by
        }

