"""
Document embedding generation and management.
"""

import logging
from typing import List

from langchain_community.embeddings import HuggingFaceEmbeddings

logger = logging.getLogger(__name__)

class DocumentEmbedder:
    """
    Handles the generation of embeddings for document chunks.
    """

    def __init__(
        self,
        embedding_model: str = "all-MiniLM-L6-v2",
        device: str = "cpu",
        **kwargs  # Accept additional parameters for backward compatibility
    ):
        """
        Initialize the document embedder.

        Args:
            embedding_model: The name of the embedding model to use
            device: Device to use for HuggingFace embeddings ('cpu' or 'cuda')
            **kwargs: Additional parameters for backward compatibility (ignored)
        """
        self.embedding_model = embedding_model

        # Initialize HuggingFace embeddings
        self.embeddings = HuggingFaceEmbeddings(
            model_name=embedding_model,
            model_kwargs={"device": device}
        )
        logger.info(f"Using HuggingFace embeddings with model: {embedding_model}")

    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for a list of texts.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors
        """
        try:
            embeddings = self.embeddings.embed_documents(texts)
            logger.info(f"Generated {len(embeddings)} embeddings")
            return embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise
