"""
Response generation using retrieved context and LLMs.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional

from langchain.schema.document import Document
from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import <PERSON>t<PERSON>penA<PERSON>
from langchain_google_vertexai import ChatVertexAI
from langchain.chains.question_answering import load_qa_chain
from google.oauth2 import service_account

logger = logging.getLogger(__name__)

class ResponseGenerator:
    """
    Generates responses to user queries using retrieved context and LLMs.
    """

    def __init__(
        self,
        model_name: str = "gpt-3.5-turbo",
        temperature: float = 0.1,
        openai_api_key: Optional[str] = None,
        google_api_key: Optional[str] = None,
        google_service_account_file: Optional[str] = None,
        google_project_id: Optional[str] = None,
        use_google: bool = False,
        chain_type: str = "stuff"
    ):
        """
        Initialize the response generator.

        Args:
            model_name: The name of the LLM model to use
            temperature: Temperature parameter for the LLM
            openai_api_key: OpenAI API key
            google_api_key: Google API key
            google_service_account_file: Path to Google service account JSON file
            google_project_id: Google Cloud project ID
            use_google: Whether to use Google AI models
            chain_type: Type of QA chain to use ('stuff', 'map_reduce', 'refine')
        """
        self.model_name = model_name
        self.temperature = temperature
        self.chain_type = chain_type
        self.use_google = use_google

        # Initialize the LLM
        if use_google:
            # Check if service account file is provided
            if google_service_account_file:
                try:
                    # Load service account info
                    credentials = service_account.Credentials.from_service_account_file(
                        google_service_account_file
                    )

                    # Get project ID from service account file if not provided
                    if not google_project_id:
                        with open(google_service_account_file, 'r') as f:
                            service_account_info = json.load(f)
                            google_project_id = service_account_info.get('project_id')

                    # Set credentials in environment
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = google_service_account_file

                    logger.info(f"Using Google service account for authentication with project ID: {google_project_id}")
                except Exception as e:
                    logger.error(f"Error loading Google service account file: {str(e)}")
                    raise ValueError(f"Error loading Google service account file: {str(e)}")
            elif google_api_key:
                # Set the API key in the environment if provided
                os.environ["GOOGLE_API_KEY"] = google_api_key
                logger.info("Using Google API key for authentication")
            elif not os.environ.get("GOOGLE_API_KEY") and not os.environ.get("GOOGLE_APPLICATION_CREDENTIALS"):
                raise ValueError("Either Google API key or service account file must be provided when use_google is True")

            # Use Google's Gemini model
            try:
                # Try different LLM models that might be available
                try:
                    self.llm = ChatVertexAI(
                        model_name="gemini-2.0-flash-001",
                        temperature=temperature,
                        project=google_project_id
                    )
                    logger.info("Using Google AI 'gemini-2.0-flash-001' model")
                except Exception as model_error:
                    logger.warning(f"Failed to use 'gemini-2.0-flash-001' model: {str(model_error)}")
                    # Try another model
                    try:
                        self.llm = ChatVertexAI(
                            model_name="chat-bison",  # Try an alternative model
                            temperature=temperature,
                            project=google_project_id
                        )
                        logger.info("Using Google AI 'chat-bison' model")
                    except Exception as alt_model_error:
                        logger.warning(f"Failed to use 'chat-bison' model: {str(alt_model_error)}")
                        # Fall back to a simple mock LLM if all Google models fail
                        from langchain.llms.fake import FakeListLLM
                        logger.warning("Falling back to a mock LLM as all Google models failed")
                        self.llm = FakeListLLM(
                            responses=["I'm a mock LLM because Google AI models are not available for your project."]
                        )
                # Log message already added in the try block
            except Exception as e:
                logger.error(f"Error initializing Google AI model: {str(e)}")
                raise ValueError(f"Error initializing Google AI model: {str(e)}")
        else:
            if not openai_api_key and not os.environ.get("OPENAI_API_KEY"):
                raise ValueError("OpenAI API key must be provided when use_google is False")

            self.llm = ChatOpenAI(
                model_name=model_name,
                temperature=temperature,
                openai_api_key=openai_api_key or os.environ.get("OPENAI_API_KEY")
            )
            logger.info(f"Using OpenAI model: {model_name}")

        # Create the QA chain
        self.qa_chain = load_qa_chain(
            llm=self.llm,
            chain_type=chain_type
        )

        logger.info(f"Initialized response generator with model: {model_name}, chain type: {chain_type}")

        # Define a custom prompt for direct generation with conversation history
        self.custom_prompt = PromptTemplate(
            input_variables=["context", "question", "conversation_history", "has_images"],
            template="""
            You are a helpful assistant that provides accurate information based on the given context.

            Your task is to answer the user's question while maintaining the flow of the conversation.

            Context from relevant documents:
            {context}

            Previous Conversation History:
            {conversation_history}

            Current Question: {question}

            Instructions:
            1. First, analyze the conversation history to understand the context of the current question
            2. Then, use the provided document context to formulate an accurate answer
            3. Maintain continuity with previous messages - refer back to earlier parts of the conversation when relevant
            4. If the user refers to something mentioned earlier, make sure to acknowledge and build upon it
            5. If the context doesn't contain the information needed, acknowledge that and provide the best response you can
            6. Never make up information that isn't in the provided context
            7. Keep your answers helpful, concise, and directly relevant to the question

            Answer:
            """
        )

        self.custom_chain = LLMChain(
            llm=self.llm,
            prompt=self.custom_prompt
        )

    def generate_response(
        self,
        query: str,
        documents: List[Document],
        conversation_history: str = "",
        use_custom_prompt: bool = True,
        include_document_references: bool = True
    ) -> Dict[str, Any]:
        """
        Generate a response to a user query using retrieved documents and conversation history.

        Args:
            query: The user query
            documents: List of retrieved Document objects
            conversation_history: String representation of previous conversation messages
            use_custom_prompt: Whether to use the custom prompt
            include_document_references: Whether to include document references in the response

        Returns:
            Dictionary containing response text, document references, and image references
        """
        try:
            if not documents:
                logger.warning("No documents provided for response generation")
                return {
                    "text": "I don't have enough information to answer that question.",
                    "images": [],
                    "document_references": []
                }

            # Combine document content
            context = "\n\n".join([doc.page_content for doc in documents])

            # No image handling - set empty values
            images = []
            image_info = "No images are available in this system."

            # Generate response
            response_text = ""
            if use_custom_prompt:
                response_text = self.custom_chain.run(
                    context=context,
                    question=query,
                    conversation_history=conversation_history,
                    has_images=image_info
                )
            else:
                response_text = self.qa_chain(
                    {"input_documents": documents, "question": query},
                    return_only_outputs=True
                )["output_text"]

            logger.info(f"Generated response for query: {query}")

            # Log the response text for debugging
            logger.debug(f"Raw response text: {response_text}")

            # Extract document references if requested
            document_references = []
            if include_document_references:
                # Get unique document IDs from the retrieved documents
                doc_ids = set()
                for doc in documents:
                    doc_id = doc.metadata.get("document_id")
                    if doc_id and doc_id not in doc_ids:
                        doc_ids.add(doc_id)
                        # Add document reference with available metadata
                        document_references.append({
                            "document_id": doc_id,
                            "filename": doc.metadata.get("file_name", "Unknown"),
                            "s3_key": doc.metadata.get("s3_key", None),
                            "page": doc.metadata.get("page", None),
                            "score": doc.metadata.get("score", None)
                        })

                logger.info(f"Added {len(document_references)} document references to response")

            # Return response with document references
            return {
                "text": response_text,
                "images": [],
                "document_references": document_references
            }
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                "text": f"I encountered an error while generating a response: {str(e)}",
                "images": [],
                "document_references": []
            }
