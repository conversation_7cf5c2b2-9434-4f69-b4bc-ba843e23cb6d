# Core dependencies
fastapi>=0.95.0
uvicorn>=0.21.1
pydantic>=2.0.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Database
sqlalchemy>=2.0.0
pymysql>=1.0.3
cryptography>=40.0.0

# Storage
boto3>=1.26.0

# PDF processing
pypdf>=3.15.0
pdfminer.six>=20221105
langchain>=0.0.267
langchain-community>=0.0.1
langchain-google-vertexai>=0.0.1
langchain-openai>=0.0.1
hf_xet==1.0.3
PyMuPDF>=1.22.5  # For PDF image extraction - may require additional system dependencies in Docker

# Embeddings
sentence-transformers>=2.2.2
chromadb>=0.4.13
langchain-community>=0.0.10  # Ensure we have the latest version with filter_complex_metadata

# Google AI
google-cloud-aiplatform>=1.36.0
google-auth>=2.22.0
vertexai>=0.0.1

# OpenAI (optional)
openai>=0.27.0

# Utilities
numpy>=1.24.0
pandas>=2.0.0
tqdm>=4.65.0
Pillow>=9.5.0  # For image processing
