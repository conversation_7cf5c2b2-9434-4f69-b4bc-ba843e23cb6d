version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ./chatbot_support_backend
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8000:8000"
    environment:
      - ENV=${ENV:-local}

      # Database configuration
      - DB_HOST=${DB_HOST:-db}
      - DB_PORT=${DB_PORT:-3306}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-rootpassword}
      - DB_NAME=${DB_NAME:-ragchatbot}
      - DATABASE_LOGGING=${DATABASE_LOGGING:-true}

      # AWS S3 configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_PREFIX=${S3_PREFIX:-chatbot_support}
      - DB_SECRET_ARN=${DB_SECRET_ARN}

      # Vector store configuration
      - VECTOR_STORE_TYPE=${VECTOR_STORE_TYPE:-chroma}
      - VECTOR_STORE_DIR=${VECTOR_STORE_DIR:-/app/central_vector_store}
      - USER_DATA_DIR=${USER_DATA_DIR:-/app/user_data}

      # JWT configuration
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-change-in-production}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=${JWT_ACCESS_TOKEN_EXPIRE_MINUTES:-30}

      # Google API configuration
      - GOOGLE_SERVICE_ACCOUNT_FILE=/app/google_credentials.json
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}

      # CORS configuration
      - CORS_ORIGINS=${CORS_ORIGINS:-*}

      # Other settings
      - PYTHONUNBUFFERED=1
    volumes:
      - ${PWD}/google_credentials.json:/app/google_credentials.json:ro
      - data-volume:/app/data
      - vector-store-volume:/app/central_vector_store
      - user-data-volume:/app/user_data
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/docs')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Frontend service (React-based)
  frontend:
    build:
      context: ./chatbot_support_frontend
      dockerfile: Dockerfile
    restart: always
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
      - REACT_APP_ENV=${REACT_APP_ENV:-production}
      - REACT_APP_VERSION=${REACT_APP_VERSION:-1.0.0}
      - BACKEND_API_URL=${BACKEND_API_URL:-http://api:8000}
    volumes:
      - ./chatbot_support_frontend/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      backend:
        condition: service_healthy

  # Database service
  db:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_ROOT_USER=root
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=ragchatbot
    volumes:
      - mysql-data:/var/lib/mysql
      - ./init_scripts/db:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-prootpassword"]
      interval: 10s
      timeout: 5s
      retries: 5

  # LocalStack service for AWS S3
  localstack:
    container_name: localstack-main
    image: localstack/localstack
    ports:
      - "4566:4566"
      - "4510-4559:4510-4559"
    environment:
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - ./localstack/s3:/var/lib/localstack
      - ./init_scripts/localstack:/etc/localstack/init/ready.d

volumes:
  data-volume:
    driver: local
  user-data-volume:
    driver: local
  vector-store-volume:
    driver: local
  mysql-data:
    driver: local
